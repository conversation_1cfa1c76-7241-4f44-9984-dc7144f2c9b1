import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'asl_flutter_input_platform_interface.dart';

/// An implementation of [AslFlutterInputPlatform] that uses method channels.
class MethodChannelAslFlutterInput extends AslFlutterInputPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('com.citrus.audio/asl_flutter_input');

  @override
  Future<String> configure(String projectId, String serviceAccountJson) async {
    try {
      final result = await methodChannel.invokeMethod<String>('configure', {
        'projectId': projectId,
        'serviceAccountJson': serviceAccountJson,
      });
      return result ?? 'Configuration completed';
    } on PlatformException catch (e) {
      throw Exception('Failed to configure: ${e.message}');
    }
  }

  @override
  Future<String> initAudioTranscription() async {
    try {
      final result = await methodChannel.invokeMethod<String>('initAudioTranscription');
      return result ?? 'Audio transcription initialized';
    } on PlatformException catch (e) {
      throw Exception('Failed to initialize audio transcription: ${e.message}');
    }
  }

  @override
  Future<String> stopListening() async {
    try {
      final result = await methodChannel.invokeMethod<String>('stopListening');
      return result ?? 'Listening stopped';
    } on PlatformException catch (e) {
      throw Exception('Failed to stop listening: ${e.message}');
    }
  }

  @override
  Future<String> getTranscribedText() async {
    try {
      final result = await methodChannel.invokeMethod<String>('getTranscribedText');
      return result ?? '';
    } on PlatformException catch (e) {
      throw Exception('Failed to get transcribed text: ${e.message}');
    }
  }

  @override
  Future<String> setLanguageCode(String languageCode) async {
    try {
      final result = await methodChannel.invokeMethod<String>('setLanguageCode', {
        'languageCode': languageCode,
      });
      return result ?? 'Language code set';
    } on PlatformException catch (e) {
      throw Exception('Failed to set language code: ${e.message}');
    }
  }

  @override
  Future<String> testAuthentication(String projectId, String serviceAccountJson) async {
    try {
      final result = await methodChannel.invokeMethod<String>('testAuthentication', {
        'projectId': projectId,
        'serviceAccountJson': serviceAccountJson,
      });
      return result ?? 'Authentication test completed';
    } on PlatformException catch (e) {
      throw Exception('Authentication test failed: ${e.message}');
    }
  }

  @override
  Future<String> getDiagnosticReport() async {
    try {
      final result = await methodChannel.invokeMethod<String>('getDiagnosticReport');
      return result ?? 'No diagnostic report available';
    } on PlatformException catch (e) {
      throw Exception('Failed to get diagnostic report: ${e.message}');
    }
  }

  @override
  Future<bool> isConfigured() async {
    try {
      final result = await methodChannel.invokeMethod<bool>('isConfigured');
      return result ?? false;
    } on PlatformException catch (e) {
      throw Exception('Failed to check configuration status: ${e.message}');
    }
  }

  @override
  Future<bool> isListening() async {
    try {
      final result = await methodChannel.invokeMethod<bool>('isListening');
      return result ?? false;
    } on PlatformException catch (e) {
      throw Exception('Failed to check listening status: ${e.message}');
    }
  }

  @override
  Future<String> getVersion() async {
    try {
      final result = await methodChannel.invokeMethod<String>('getVersion');
      return result ?? 'Unknown';
    } on PlatformException catch (e) {
      throw Exception('Failed to get version: ${e.message}');
    }
  }

  @override
  Future<String> reset() async {
    try {
      final result = await methodChannel.invokeMethod<String>('reset');
      return result ?? 'Reset completed';
    } on PlatformException catch (e) {
      throw Exception('Failed to reset: ${e.message}');
    }
  }
}
