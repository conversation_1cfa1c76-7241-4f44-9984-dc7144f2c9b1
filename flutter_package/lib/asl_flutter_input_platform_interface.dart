import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'asl_flutter_input_method_channel.dart';

/// The interface that implementations of asl_flutter_input must implement.
///
/// Platform implementations should extend this class rather than implement it as `asl_flutter_input`
/// does not consider newly added methods to be breaking changes. Extending this class
/// (using `extends`) ensures that the subclass will get the default implementation, while
/// platform implementations that `implements` this interface will be broken by newly added
/// [AslFlutterInputPlatform] methods.
abstract class AslFlutterInputPlatform extends PlatformInterface {
  /// Constructs a AslFlutterInputPlatform.
  AslFlutterInputPlatform() : super(token: _token);

  static final Object _token = Object();

  static AslFlutterInputPlatform _instance = MethodChannelAslFlutterInput();

  /// The default instance of [AslFlutterInputPlatform] to use.
  ///
  /// Defaults to [MethodChannelAslFlutterInput].
  static AslFlutterInputPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [AslFlutterInputPlatform] when
  /// they register themselves.
  static set instance(AslFlutterInputPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  /// Configure the library with project ID and service account JSON
  Future<String> configure(String projectId, String serviceAccountJson);

  /// Initialize audio transcription
  Future<String> initAudioTranscription();

  /// Stop listening for audio
  Future<String> stopListening();

  /// Get the current transcribed text
  Future<String> getTranscribedText();

  /// Set the language code for speech recognition (default: ar-SA)
  Future<String> setLanguageCode(String languageCode);

  /// Test authentication with provided credentials
  Future<String> testAuthentication(String projectId, String serviceAccountJson);

  /// Get diagnostic report for troubleshooting
  Future<String> getDiagnosticReport();

  /// Check if the library is configured
  Future<bool> isConfigured();

  /// Check if the library is currently listening
  Future<bool> isListening();

  /// Get the library version
  Future<String> getVersion();

  /// Reset the library configuration
  Future<String> reset();
}
