import 'dart:async';
import 'dart:convert';
import 'asl_flutter_input_platform_interface.dart';

/// Flutter plugin for ASL Android Input library providing real-time speech-to-text transcription.
///
/// This plugin provides access to the ASL Android Input library which uses Google Cloud Speech API
/// for real-time speech recognition with support for Arabic (ar-SA) as the default language.
class AslFlutterInput {
  /// Default language code for Arabic (Saudi Arabia)
  static const String defaultLanguageCode = 'ar-SA';

  /// Configure the library with project ID and service account JSON
  ///
  /// [projectId] - Google Cloud project ID
  /// [serviceAccountJson] - Service account JSON as a string
  ///
  /// Returns a success message or throws an exception on failure
  static Future<String> configure(String projectId, String serviceAccountJson) async {
    _validateProjectId(projectId);
    _validateServiceAccountJson(serviceAccountJson);
    
    return AslFlutterInputPlatform.instance.configure(projectId, serviceAccountJson);
  }

  /// Initialize audio transcription
  ///
  /// Must be called after [configure] and before starting transcription.
  /// Returns a success message or throws an exception on failure
  static Future<String> initAudioTranscription() async {
    return AslFlutterInputPlatform.instance.initAudioTranscription();
  }

  /// Stop listening for audio
  ///
  /// Stops the current transcription session.
  /// Returns a success message or throws an exception on failure
  static Future<String> stopListening() async {
    return AslFlutterInputPlatform.instance.stopListening();
  }

  /// Get the current transcribed text
  ///
  /// Returns the latest transcribed text or an empty string if no text is available
  static Future<String> getTranscribedText() async {
    return AslFlutterInputPlatform.instance.getTranscribedText();
  }

  /// Set the language code for speech recognition
  ///
  /// [languageCode] - Language code (e.g., 'ar-SA', 'en-US', 'fr-FR')
  /// Default is 'ar-SA' for Arabic (Saudi Arabia)
  ///
  /// Returns a success message or throws an exception on failure
  static Future<String> setLanguageCode(String languageCode) async {
    if (languageCode.isEmpty) {
      throw ArgumentError('Language code cannot be empty');
    }
    return AslFlutterInputPlatform.instance.setLanguageCode(languageCode);
  }

  /// Test authentication with provided credentials
  ///
  /// [projectId] - Google Cloud project ID
  /// [serviceAccountJson] - Service account JSON as a string
  ///
  /// Returns a success message if authentication passes, throws an exception on failure
  static Future<String> testAuthentication(String projectId, String serviceAccountJson) async {
    _validateProjectId(projectId);
    _validateServiceAccountJson(serviceAccountJson);
    
    return AslFlutterInputPlatform.instance.testAuthentication(projectId, serviceAccountJson);
  }

  /// Get diagnostic report for troubleshooting
  ///
  /// Returns detailed diagnostic information about the library state,
  /// configuration, and any errors that may have occurred
  static Future<String> getDiagnosticReport() async {
    return AslFlutterInputPlatform.instance.getDiagnosticReport();
  }

  /// Check if the library is configured
  ///
  /// Returns true if the library has been successfully configured with
  /// valid credentials, false otherwise
  static Future<bool> isConfigured() async {
    return AslFlutterInputPlatform.instance.isConfigured();
  }

  /// Check if the library is currently listening
  ///
  /// Returns true if the library is actively listening for audio input,
  /// false otherwise
  static Future<bool> isListening() async {
    return AslFlutterInputPlatform.instance.isListening();
  }

  /// Get the library version
  ///
  /// Returns the version string of the underlying ASL library
  static Future<String> getVersion() async {
    return AslFlutterInputPlatform.instance.getVersion();
  }

  /// Reset the library configuration
  ///
  /// Clears all configuration and stops any active transcription.
  /// The library will need to be reconfigured before use.
  ///
  /// Returns a success message or throws an exception on failure
  static Future<String> reset() async {
    return AslFlutterInputPlatform.instance.reset();
  }

  /// Convenience method to configure and start transcription with Arabic as default
  ///
  /// [projectId] - Google Cloud project ID
  /// [serviceAccountJson] - Service account JSON as a string
  /// [languageCode] - Optional language code (defaults to 'ar-SA')
  ///
  /// Returns a success message or throws an exception on failure
  static Future<String> startListening(String projectId, String serviceAccountJson, {String? languageCode}) async {
    try {
      // Test authentication first
      await testAuthentication(projectId, serviceAccountJson);
      
      // Configure the library
      await configure(projectId, serviceAccountJson);
      
      // Set language code (default to Arabic)
      await setLanguageCode(languageCode ?? defaultLanguageCode);
      
      // Initialize audio transcription
      await initAudioTranscription();
      
      return 'Successfully started listening with language: ${languageCode ?? defaultLanguageCode}';
    } catch (e) {
      throw Exception('Failed to start listening: $e');
    }
  }

  /// Convenience method to get transcription results with polling
  ///
  /// [intervalMs] - Polling interval in milliseconds (default: 500ms)
  /// [maxAttempts] - Maximum number of polling attempts (default: 10)
  ///
  /// Returns the transcribed text or throws an exception on failure
  static Future<String> pollForTranscription({int intervalMs = 500, int maxAttempts = 10}) async {
    for (int i = 0; i < maxAttempts; i++) {
      final text = await getTranscribedText();
      if (text.isNotEmpty) {
        return text;
      }
      await Future.delayed(Duration(milliseconds: intervalMs));
    }
    return '';
  }

  /// Validate project ID format
  static void _validateProjectId(String projectId) {
    if (projectId.isEmpty) {
      throw ArgumentError('Project ID cannot be empty');
    }
    if (!RegExp(r'^[a-z][a-z0-9-]*[a-z0-9]$').hasMatch(projectId)) {
      throw ArgumentError('Invalid project ID format. Must contain only lowercase letters, numbers, and hyphens.');
    }
  }

  /// Validate service account JSON format
  static void _validateServiceAccountJson(String serviceAccountJson) {
    if (serviceAccountJson.isEmpty) {
      throw ArgumentError('Service account JSON cannot be empty');
    }
    
    try {
      final Map<String, dynamic> json = jsonDecode(serviceAccountJson);
      
      // Check for required fields
      final requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
      for (final field in requiredFields) {
        if (!json.containsKey(field) || json[field] == null) {
          throw ArgumentError('Service account JSON is missing required field: $field');
        }
      }
      
      if (json['type'] != 'service_account') {
        throw ArgumentError('Service account JSON must have type "service_account"');
      }
    } catch (e) {
      if (e is ArgumentError) rethrow;
      throw ArgumentError('Invalid service account JSON format: $e');
    }
  }
}
