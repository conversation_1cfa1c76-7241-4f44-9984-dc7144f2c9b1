# Testing and Validation Guide

This guide provides comprehensive testing instructions to validate the ASL Flutter Input package integration.

## Prerequisites

1. **Flutter Environment**: Flutter 2.5.0 or higher
2. **Android Device/Emulator**: API level 21 or higher
3. **Google Cloud Project**: With Speech-to-Text API enabled
4. **Service Account**: With proper permissions and JSON key file
5. **Microphone Access**: Physical device with microphone (emulator may have limitations)

## Quick Validation Checklist

### ✅ Package Integration Test

```bash
# 1. Create a new Flutter project
flutter create test_asl_integration
cd test_asl_integration

# 2. Add the package to pubspec.yaml
# dependencies:
#   asl_flutter_input:
#     path: ../path/to/your/flutter_package

# 3. Get dependencies
flutter pub get

# 4. Check for any dependency conflicts
flutter pub deps
```

### ✅ Build Test

```bash
# Test Android build
flutter build apk --debug

# Check for build errors
flutter analyze
```

### ✅ Plugin Registration Test

```dart
// Add to your test app's main.dart
import 'package:asl_flutter_input/asl_flutter_input.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Test plugin registration
    String version = await AslFlutterInput.getVersion();
    print('Plugin version: $version');
    print('✅ Plugin registration successful');
  } catch (e) {
    print('❌ Plugin registration failed: $e');
  }
  
  runApp(MyApp());
}
```

## Detailed Testing Procedures

### 1. Authentication Testing

```dart
Future<void> testAuthentication() async {
  const String projectId = 'your-test-project-id';
  const String serviceAccountJson = '''
  {
    "type": "service_account",
    "project_id": "your-project-id",
    // ... your service account JSON
  }
  ''';
  
  try {
    // Test 1: Valid credentials
    String result = await AslFlutterInput.testAuthentication(projectId, serviceAccountJson);
    print('✅ Authentication test passed: $result');
    
    // Test 2: Invalid project ID
    try {
      await AslFlutterInput.testAuthentication('invalid-project', serviceAccountJson);
      print('❌ Should have failed with invalid project ID');
    } catch (e) {
      print('✅ Correctly rejected invalid project ID: $e');
    }
    
    // Test 3: Invalid JSON
    try {
      await AslFlutterInput.testAuthentication(projectId, 'invalid-json');
      print('❌ Should have failed with invalid JSON');
    } catch (e) {
      print('✅ Correctly rejected invalid JSON: $e');
    }
    
  } catch (e) {
    print('❌ Authentication test failed: $e');
  }
}
```

### 2. Configuration Testing

```dart
Future<void> testConfiguration() async {
  try {
    // Test initial state
    bool initiallyConfigured = await AslFlutterInput.isConfigured();
    print('Initially configured: $initiallyConfigured');
    
    // Test configuration
    await AslFlutterInput.configure(projectId, serviceAccountJson);
    
    bool configuredAfter = await AslFlutterInput.isConfigured();
    print('✅ Configuration successful: $configuredAfter');
    
    // Test language setting
    await AslFlutterInput.setLanguageCode('ar-SA');
    print('✅ Language code set to Arabic');
    
    await AslFlutterInput.setLanguageCode('en-US');
    print('✅ Language code set to English');
    
  } catch (e) {
    print('❌ Configuration test failed: $e');
  }
}
```

### 3. Transcription Testing

```dart
Future<void> testTranscription() async {
  try {
    // Configure first
    await AslFlutterInput.configure(projectId, serviceAccountJson);
    await AslFlutterInput.setLanguageCode('en-US');
    
    // Start transcription
    await AslFlutterInput.initAudioTranscription();
    
    bool isListening = await AslFlutterInput.isListening();
    print('✅ Transcription started: $isListening');
    
    // Wait and check for transcription
    print('🎤 Please speak into the microphone...');
    await Future.delayed(Duration(seconds: 5));
    
    String transcribedText = await AslFlutterInput.getTranscribedText();
    print('Transcribed text: "$transcribedText"');
    
    if (transcribedText.isNotEmpty) {
      print('✅ Transcription working');
    } else {
      print('⚠️ No transcription received (may need more time or louder speech)');
    }
    
    // Stop transcription
    await AslFlutterInput.stopListening();
    
    bool isListeningAfter = await AslFlutterInput.isListening();
    print('✅ Transcription stopped: ${!isListeningAfter}');
    
  } catch (e) {
    print('❌ Transcription test failed: $e');
  }
}
```

### 4. Error Handling Testing

```dart
Future<void> testErrorHandling() async {
  try {
    // Test unconfigured state
    try {
      await AslFlutterInput.initAudioTranscription();
      print('❌ Should have failed when not configured');
    } catch (e) {
      print('✅ Correctly handled unconfigured state: $e');
    }
    
    // Test invalid language code
    await AslFlutterInput.configure(projectId, serviceAccountJson);
    try {
      await AslFlutterInput.setLanguageCode('');
      print('❌ Should have failed with empty language code');
    } catch (e) {
      print('✅ Correctly handled empty language code: $e');
    }
    
  } catch (e) {
    print('❌ Error handling test failed: $e');
  }
}
```

### 5. Diagnostic Testing

```dart
Future<void> testDiagnostics() async {
  try {
    // Get diagnostic report
    String report = await AslFlutterInput.getDiagnosticReport();
    print('Diagnostic Report:');
    print(report);
    
    // Verify report contains expected information
    if (report.contains('Version:') && 
        report.contains('Configured:') && 
        report.contains('Listening:')) {
      print('✅ Diagnostic report contains expected fields');
    } else {
      print('❌ Diagnostic report missing expected fields');
    }
    
  } catch (e) {
    print('❌ Diagnostic test failed: $e');
  }
}
```

## Performance Testing

### Memory Usage Test

```dart
Future<void> testMemoryUsage() async {
  print('🔍 Testing memory usage...');
  
  for (int i = 0; i < 10; i++) {
    await AslFlutterInput.configure(projectId, serviceAccountJson);
    await AslFlutterInput.initAudioTranscription();
    await Future.delayed(Duration(seconds: 2));
    await AslFlutterInput.stopListening();
    await AslFlutterInput.reset();
    
    print('Cycle $i completed');
  }
  
  print('✅ Memory usage test completed');
}
```

### Language Switching Test

```dart
Future<void> testLanguageSwitching() async {
  final languages = ['ar-SA', 'en-US', 'fr-FR', 'es-ES'];
  
  await AslFlutterInput.configure(projectId, serviceAccountJson);
  
  for (String lang in languages) {
    try {
      await AslFlutterInput.setLanguageCode(lang);
      await AslFlutterInput.initAudioTranscription();
      await Future.delayed(Duration(seconds: 2));
      await AslFlutterInput.stopListening();
      
      print('✅ Language $lang tested successfully');
    } catch (e) {
      print('❌ Language $lang failed: $e');
    }
  }
}
```

## Integration Testing with Flutter App

### Complete Integration Test

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:your_app/main.dart' as app;
import 'package:asl_flutter_input/asl_flutter_input.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('ASL Flutter Input Integration Tests', () {
    testWidgets('Plugin loads and responds', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test plugin version
      String version = await AslFlutterInput.getVersion();
      expect(version, isNotEmpty);
    });

    testWidgets('Configuration works', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test configuration
      await AslFlutterInput.configure(projectId, serviceAccountJson);
      bool configured = await AslFlutterInput.isConfigured();
      expect(configured, isTrue);
    });
  });
}
```

## Troubleshooting Common Issues

### Issue: Plugin not found

**Solution:**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk
```

### Issue: Authentication failures

**Solution:**
```dart
// Check diagnostic report
String report = await AslFlutterInput.getDiagnosticReport();
print(report);

// Verify service account JSON format
// Ensure project ID matches the JSON
// Check Google Cloud API permissions
```

### Issue: No transcription results

**Solution:**
```dart
// Check microphone permissions
// Verify network connectivity
// Test with louder/clearer speech
// Check language code setting
```

## Validation Checklist

- [ ] Package builds without errors
- [ ] Plugin registers correctly
- [ ] Authentication test passes
- [ ] Configuration works
- [ ] Language setting works
- [ ] Transcription starts and stops
- [ ] Text transcription works
- [ ] Error handling works correctly
- [ ] Diagnostic report provides useful information
- [ ] Memory usage is stable
- [ ] Multiple language codes work

## Performance Benchmarks

Expected performance metrics:
- **Initialization time**: < 2 seconds
- **Transcription latency**: < 500ms
- **Memory usage**: < 50MB additional
- **CPU usage**: < 10% during transcription

## Automated Testing Script

```bash
#!/bin/bash
echo "🚀 Starting ASL Flutter Input validation..."

# Build test
echo "📦 Testing build..."
flutter build apk --debug

if [ $? -eq 0 ]; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

# Run tests
echo "🧪 Running tests..."
flutter test

echo "✅ Validation complete!"
```

Run this script to perform automated validation of your integration.
