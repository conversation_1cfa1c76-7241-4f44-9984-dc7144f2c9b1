package com.citrus.audio;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Main wrapper class for ASL Audio Library functionality.
 * This class provides a simplified static API for Flutter integration.
 */
public class AslAudioLibrary {
    private static final String TAG = "AslAudioLibrary";
    private static final String VERSION = "1.0.4";
    
    // Configuration state
    private static final AtomicBoolean isConfigured = new AtomicBoolean(false);
    private static final AtomicBoolean isListening = new AtomicBoolean(false);
    private static final AtomicReference<String> currentProjectId = new AtomicReference<>();
    private static final AtomicReference<String> currentServiceAccountJson = new AtomicReference<>();
    private static final AtomicReference<String> currentLanguageCode = new AtomicReference<>("ar-SA");
    private static final AtomicReference<String> lastTranscribedText = new AtomicReference<>("");
    private static final AtomicReference<String> lastError = new AtomicReference<>("");
    
    // Activity reference for audio recording
    private static Activity currentActivity;

    /**
     * Configure the library with Google Cloud credentials
     */
    public static void configure(String projectId, String serviceAccountJson) {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("Project ID cannot be null or empty");
        }
        if (serviceAccountJson == null || serviceAccountJson.trim().isEmpty()) {
            throw new IllegalArgumentException("Service account JSON cannot be null or empty");
        }
        
        try {
            Log.d(TAG, "Configuring ASL Audio Library with project: " + projectId);
            
            // Validate JSON format
            if (!isValidServiceAccountJson(serviceAccountJson)) {
                throw new IllegalArgumentException("Invalid service account JSON format");
            }
            
            currentProjectId.set(projectId);
            currentServiceAccountJson.set(serviceAccountJson);
            isConfigured.set(true);
            lastError.set("");
            
            Log.d(TAG, "Configuration successful");
        } catch (Exception e) {
            Log.e(TAG, "Configuration failed", e);
            lastError.set("Configuration failed: " + e.getMessage());
            throw new IllegalArgumentException("Configuration failed: " + e.getMessage());
        }
    }

    /**
     * Initialize audio transcription
     */
    public static void initAudioTranscription(Activity activity) {
        if (!isConfigured.get()) {
            throw new IllegalStateException("Library must be configured before initializing transcription");
        }
        if (activity == null) {
            throw new IllegalStateException("Activity cannot be null");
        }
        
        try {
            Log.d(TAG, "Initializing audio transcription");
            currentActivity = activity;
            
            // Set up the MainActivity static variables
            MainActivity.PROJECT_ID = currentProjectId.get();
            MainActivity.SERVICE_ACCOUNT_JSON = currentServiceAccountJson.get();
            MainActivity.currentLanguageCode = currentLanguageCode.get();
            
            // Initialize the recognition session
            MainActivity.constructRepeatingRecognitionSession(activity);
            
            // Start recording
            MainActivity.startRecording();
            
            isListening.set(true);
            lastError.set("");
            
            Log.d(TAG, "Audio transcription initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize audio transcription", e);
            lastError.set("Initialization failed: " + e.getMessage());
            throw new IllegalStateException("Failed to initialize audio transcription: " + e.getMessage());
        }
    }

    /**
     * Stop listening for audio
     */
    public static void stopListening() {
        try {
            Log.d(TAG, "Stopping audio transcription");
            
            if (isListening.get()) {
                MainActivity.stopRecording();
                isListening.set(false);
            }
            
            Log.d(TAG, "Audio transcription stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping transcription", e);
            lastError.set("Stop listening failed: " + e.getMessage());
        }
    }

    /**
     * Get the current transcribed text
     */
    public static String getTranscribedText() {
        try {
            String text = MainActivity.getLatestTranscription();
            if (text != null && !text.isEmpty()) {
                lastTranscribedText.set(text);
                return text;
            }
            return lastTranscribedText.get();
        } catch (Exception e) {
            Log.e(TAG, "Error getting transcribed text", e);
            lastError.set("Get transcribed text failed: " + e.getMessage());
            return "";
        }
    }

    /**
     * Set the language code for speech recognition
     */
    public static void setLanguageCode(String languageCode) {
        if (languageCode == null || languageCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Language code cannot be null or empty");
        }
        
        try {
            Log.d(TAG, "Setting language code to: " + languageCode);
            currentLanguageCode.set(languageCode);
            MainActivity.currentLanguageCode = languageCode;
            
            // If already listening, restart with new language
            if (isListening.get() && currentActivity != null) {
                stopListening();
                Thread.sleep(500); // Brief pause
                initAudioTranscription(currentActivity);
            }
            
            Log.d(TAG, "Language code set successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting language code", e);
            lastError.set("Set language code failed: " + e.getMessage());
            throw new IllegalArgumentException("Failed to set language code: " + e.getMessage());
        }
    }

    /**
     * Test authentication with provided credentials
     */
    public static boolean testAuthentication(String projectId, String serviceAccountJson) {
        try {
            Log.d(TAG, "Testing authentication");
            
            if (projectId == null || projectId.trim().isEmpty()) {
                lastError.set("Project ID is null or empty");
                return false;
            }
            
            if (serviceAccountJson == null || serviceAccountJson.trim().isEmpty()) {
                lastError.set("Service account JSON is null or empty");
                return false;
            }
            
            if (!isValidServiceAccountJson(serviceAccountJson)) {
                lastError.set("Invalid service account JSON format");
                return false;
            }
            
            // TODO: Add actual Google Cloud authentication test
            // For now, just validate the format
            Log.d(TAG, "Authentication test passed");
            lastError.set("");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Authentication test failed", e);
            lastError.set("Authentication test failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get diagnostic report for troubleshooting
     */
    public static String getDiagnosticReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== ASL Audio Library Diagnostic Report ===\n");
        report.append("Version: ").append(VERSION).append("\n");
        report.append("Configured: ").append(isConfigured.get()).append("\n");
        report.append("Listening: ").append(isListening.get()).append("\n");
        report.append("Language Code: ").append(currentLanguageCode.get()).append("\n");
        report.append("Project ID: ").append(currentProjectId.get() != null ? "Set" : "Not set").append("\n");
        report.append("Service Account: ").append(currentServiceAccountJson.get() != null ? "Set" : "Not set").append("\n");
        report.append("Activity: ").append(currentActivity != null ? "Available" : "Not available").append("\n");
        report.append("Last Error: ").append(lastError.get()).append("\n");
        report.append("Last Transcribed Text Length: ").append(lastTranscribedText.get().length()).append("\n");
        report.append("=== End Report ===");
        
        return report.toString();
    }

    /**
     * Check if the library is configured
     */
    public static boolean isConfigured() {
        return isConfigured.get();
    }

    /**
     * Check if the library is currently listening
     */
    public static boolean isListening() {
        return isListening.get();
    }

    /**
     * Get the library version
     */
    public static String getVersion() {
        return VERSION;
    }

    /**
     * Reset the library configuration
     */
    public static void reset() {
        try {
            Log.d(TAG, "Resetting library");
            
            stopListening();
            
            isConfigured.set(false);
            currentProjectId.set(null);
            currentServiceAccountJson.set(null);
            currentLanguageCode.set("ar-SA");
            lastTranscribedText.set("");
            lastError.set("");
            currentActivity = null;
            
            Log.d(TAG, "Library reset completed");
        } catch (Exception e) {
            Log.e(TAG, "Error during reset", e);
            lastError.set("Reset failed: " + e.getMessage());
        }
    }

    /**
     * Validate service account JSON format
     */
    private static boolean isValidServiceAccountJson(String json) {
        try {
            // Basic validation - check if it contains required fields
            return json.contains("\"type\"") && 
                   json.contains("\"project_id\"") && 
                   json.contains("\"private_key\"") && 
                   json.contains("\"client_email\"");
        } catch (Exception e) {
            return false;
        }
    }
}
