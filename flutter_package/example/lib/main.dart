import 'package:flutter/material.dart';
import 'package:asl_flutter_input/asl_flutter_input.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ASL Flutter Input Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: MyHomePage(title: 'ASL Speech Recognition Demo'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  MyHomePage({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  String _transcribedText = '';
  bool _isListening = false;
  bool _isConfigured = false;
  String _status = 'Not configured';
  
  // Replace these with your actual Google Cloud credentials
  final String _projectId = 'your-google-cloud-project-id';
  final String _serviceAccountJson = '''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
''';

  @override
  void initState() {
    super.initState();
    _checkConfiguration();
  }

  Future<void> _checkConfiguration() async {
    try {
      bool configured = await AslFlutterInput.isConfigured();
      setState(() {
        _isConfigured = configured;
        _status = configured ? 'Configured' : 'Not configured';
      });
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _testAuthentication() async {
    try {
      setState(() {
        _status = 'Testing authentication...';
      });
      
      String result = await AslFlutterInput.testAuthentication(_projectId, _serviceAccountJson);
      setState(() {
        _status = 'Auth test: $result';
      });
    } catch (e) {
      setState(() {
        _status = 'Auth test failed: $e';
      });
    }
  }

  Future<void> _configure() async {
    try {
      setState(() {
        _status = 'Configuring...';
      });
      
      String result = await AslFlutterInput.configure(_projectId, _serviceAccountJson);
      await AslFlutterInput.setLanguageCode('ar-SA'); // Set Arabic as default
      
      bool configured = await AslFlutterInput.isConfigured();
      setState(() {
        _isConfigured = configured;
        _status = 'Configuration: $result';
      });
    } catch (e) {
      setState(() {
        _status = 'Configuration failed: $e';
      });
    }
  }

  Future<void> _startListening() async {
    try {
      setState(() {
        _status = 'Starting transcription...';
      });
      
      String result = await AslFlutterInput.startListening(_projectId, _serviceAccountJson);
      
      bool listening = await AslFlutterInput.isListening();
      setState(() {
        _isListening = listening;
        _status = listening ? 'Listening...' : 'Failed to start listening';
      });
      
      // Start polling for transcription results
      if (listening) {
        _pollForTranscription();
      }
    } catch (e) {
      setState(() {
        _status = 'Failed to start listening: $e';
      });
    }
  }

  Future<void> _stopListening() async {
    try {
      String result = await AslFlutterInput.stopListening();
      
      bool listening = await AslFlutterInput.isListening();
      setState(() {
        _isListening = listening;
        _status = 'Stopped listening';
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to stop listening: $e';
      });
    }
  }

  Future<void> _pollForTranscription() async {
    while (_isListening) {
      try {
        String text = await AslFlutterInput.getTranscribedText();
        if (text.isNotEmpty && text != _transcribedText) {
          setState(() {
            _transcribedText = text;
          });
        }
        
        // Check if still listening
        bool listening = await AslFlutterInput.isListening();
        if (!listening) {
          setState(() {
            _isListening = false;
            _status = 'Stopped listening';
          });
          break;
        }
        
        await Future.delayed(Duration(milliseconds: 500));
      } catch (e) {
        setState(() {
          _status = 'Polling error: $e';
        });
        break;
      }
    }
  }

  Future<void> _getDiagnosticReport() async {
    try {
      String report = await AslFlutterInput.getDiagnosticReport();
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Diagnostic Report'),
          content: SingleChildScrollView(
            child: Text(report),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      setState(() {
        _status = 'Failed to get diagnostic report: $e';
      });
    }
  }

  Future<void> _reset() async {
    try {
      String result = await AslFlutterInput.reset();
      setState(() {
        _isConfigured = false;
        _isListening = false;
        _transcribedText = '';
        _status = 'Reset completed';
      });
    } catch (e) {
      setState(() {
        _status = 'Reset failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status: $_status', style: TextStyle(fontWeight: FontWeight.bold)),
                    SizedBox(height: 8),
                    Text('Configured: $_isConfigured'),
                    Text('Listening: $_isListening'),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _testAuthentication,
                  child: Text('Test Auth'),
                ),
                ElevatedButton(
                  onPressed: _configure,
                  child: Text('Configure'),
                ),
                ElevatedButton(
                  onPressed: _isConfigured ? _startListening : null,
                  child: Text('Start'),
                ),
                ElevatedButton(
                  onPressed: _isListening ? _stopListening : null,
                  child: Text('Stop'),
                ),
                ElevatedButton(
                  onPressed: _getDiagnosticReport,
                  child: Text('Diagnostics'),
                ),
                ElevatedButton(
                  onPressed: _reset,
                  child: Text('Reset'),
                ),
              ],
            ),
            SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Transcribed Text:', style: TextStyle(fontWeight: FontWeight.bold)),
                      SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _transcribedText.isEmpty ? 'No transcription yet...' : _transcribedText,
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
