# Flutter Integration Guide for ASL Android Input

This guide explains how to integrate the ASL Android Input library with your Flutter application to resolve the Base64 decoding authentication error.

## Problem Description

The original error occurred because:
1. The Flutter method channel implementation was missing
2. Service account JSON was not being properly passed from Flutter to Android
3. The Android code fell back to loading from assets, which caused Base64 decoding errors

## Solution Overview

The fix includes:
1. **Flutter Method Channel Implementation**: Added proper method channel handling in MainActivity
2. **JSON Validation**: Added comprehensive validation for service account JSON
3. **Enhanced Error Handling**: Improved logging and error reporting
4. **Authentication Testing**: Added test methods to verify authentication setup

## Flutter Side Implementation

### 1. Method Channel Setup

```dart
import 'package:flutter/services.dart';

class AslFlutterInputPlugin {
  static const MethodChannel _channel = MethodChannel('com.citrus.audio/asl_flutter_input');

  // Configure the library with project ID and service account JSON
  static Future<String> configure(String projectId, String serviceAccountJson) async {
    try {
      final String result = await _channel.invokeMethod('configure', {
        'projectId': projectId,
        'serviceAccountJson': serviceAccount<PERSON>son,
      });
      return result;
    } on PlatformException catch (e) {
      throw Exception('Failed to configure: ${e.message}');
    }
  }

  // Initialize audio transcription
  static Future<String> initAudioTranscription() async {
    try {
      final String result = await _channel.invokeMethod('initAudioTranscription');
      return result;
    } on PlatformException catch (e) {
      throw Exception('Failed to initialize audio transcription: ${e.message}');
    }
  }

  // Test authentication (useful for debugging)
  static Future<String> testAuthentication(String projectId, String serviceAccountJson) async {
    try {
      final String result = await _channel.invokeMethod('testAuthentication', {
        'projectId': projectId,
        'serviceAccountJson': serviceAccountJson,
      });
      return result;
    } on PlatformException catch (e) {
      throw Exception('Authentication test failed: ${e.message}');
    }
  }

  // Get diagnostic report
  static Future<String> getDiagnosticReport() async {
    try {
      final String result = await _channel.invokeMethod('getDiagnosticReport');
      return result;
    } on PlatformException catch (e) {
      throw Exception('Failed to get diagnostic report: ${e.message}');
    }
  }

  // Stop listening
  static Future<String> stopListening() async {
    try {
      final String result = await _channel.invokeMethod('stopListening');
      return result;
    } on PlatformException catch (e) {
      throw Exception('Failed to stop listening: ${e.message}');
    }
  }

  // Get transcribed text
  static Future<String> getTranscribedText() async {
    try {
      final String result = await _channel.invokeMethod('getTranscribedText');
      return result;
    } on PlatformException catch (e) {
      throw Exception('Failed to get transcribed text: ${e.message}');
    }
  }

  // Set language code
  static Future<String> setLanguageCode(String languageCode) async {
    try {
      final String result = await _channel.invokeMethod('setLanguageCode', {
        'languageCode': languageCode,
      });
      return result;
    } on PlatformException catch (e) {
      throw Exception('Failed to set language code: ${e.message}');
    }
  }
}
```

### 2. Usage Example

```dart
class SpeechToTextExample extends StatefulWidget {
  @override
  _SpeechToTextExampleState createState() => _SpeechToTextExampleState();
}

class _SpeechToTextExampleState extends State<SpeechToTextExample> {
  String _transcribedText = '';
  bool _isListening = false;

  // Your Google Cloud project ID
  final String projectId = "your-project-id";
  
  // Your service account JSON as a string
  final String serviceAccountJson = '''
  ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ''';

  Future<void> _startListening() async {
    try {
      // First, test authentication
      await AslFlutterInputPlugin.testAuthentication(projectId, serviceAccountJson);
      print('Authentication test passed');

      // Configure the library
      await AslFlutterInputPlugin.configure(projectId, serviceAccountJson);
      print('Configuration successful');

      // Initialize audio transcription
      await AslFlutterInputPlugin.initAudioTranscription();
      print('Audio transcription initialized');

      setState(() {
        _isListening = true;
      });

      // Start polling for transcribed text
      _pollForTranscribedText();

    } catch (e) {
      print('Error starting speech recognition: $e');
      
      // Get diagnostic report for debugging
      try {
        String report = await AslFlutterInputPlugin.getDiagnosticReport();
        print('Diagnostic Report:\n$report');
      } catch (reportError) {
        print('Failed to get diagnostic report: $reportError');
      }
    }
  }

  Future<void> _stopListening() async {
    try {
      await AslFlutterInputPlugin.stopListening();
      setState(() {
        _isListening = false;
        _transcribedText = '';
      });
    } catch (e) {
      print('Error stopping speech recognition: $e');
    }
  }

  void _pollForTranscribedText() {
    if (!_isListening) return;

    Timer.periodic(Duration(milliseconds: 500), (timer) async {
      if (!_isListening) {
        timer.cancel();
        return;
      }

      try {
        String text = await AslFlutterInputPlugin.getTranscribedText();
        setState(() {
          _transcribedText = text;
        });
      } catch (e) {
        print('Error getting transcribed text: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Speech to Text')),
      body: Column(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.all(16),
              child: Text(
                _transcribedText.isEmpty ? 'No speech detected' : _transcribedText,
                style: TextStyle(fontSize: 18),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _isListening ? null : _startListening,
                child: Text('Start Listening'),
              ),
              ElevatedButton(
                onPressed: _isListening ? _stopListening : null,
                child: Text('Stop Listening'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
```

## Troubleshooting

### 1. Authentication Test
Always test authentication first:
```dart
try {
  await AslFlutterInputPlugin.testAuthentication(projectId, serviceAccountJson);
  print('Authentication successful');
} catch (e) {
  print('Authentication failed: $e');
}
```

### 2. Diagnostic Report
Get detailed diagnostic information:
```dart
String report = await AslFlutterInputPlugin.getDiagnosticReport();
print(report);
```

### 3. Common Issues

**Invalid JSON Format**: Ensure your service account JSON is properly formatted and contains all required fields.

**Project ID Mismatch**: Make sure the project ID parameter matches the project_id in your service account JSON.

**Missing Permissions**: Ensure your service account has the necessary permissions for the Speech-to-Text API.

**Network Issues**: Verify that your device has internet connectivity and can reach Google Cloud services.

## Android Manifest Permissions

Make sure your Android manifest includes the necessary permissions:

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
```

## Dependencies

The Android library now includes Flutter embedding support. Make sure your Flutter project is configured to use the embedding v2 API.
