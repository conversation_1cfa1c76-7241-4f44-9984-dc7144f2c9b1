-- ANDROID_PLATFORM not set. Defaulting to minimum supported version
16.
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out
[ 12%] Creating directories for 'external_libogg'
[ 25%] Performing download step (git clone) for 'external_libogg'
Cloning into 'src'...
HEAD is now at 9343853 Fix documentation typo.
[ 37%] Performing update step for 'external_libogg'
[ 50%] No patch step for 'external_libogg'
[ 62%] Performing configure step for 'external_libogg'
-- ANDROID_PLATFORM not set. Defaulting to minimum supported version
16.
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring libogg 1.3.3
-- Looking for include file inttypes.h
-- Looking for include file inttypes.h - found
-- Looking for include file stdint.h
-- Looking for include file stdint.h - found
-- Looking for include file sys/types.h
-- Looking for include file sys/types.h - found
-- Looking for sys/types.h
-- Looking for sys/types.h - found
-- Looking for stdint.h
-- Looking for stdint.h - found
-- Looking for stddef.h
-- Looking for stddef.h - found
-- Check size of int16_t
-- Check size of int16_t - done
-- Check size of uint16_t
-- Check size of uint16_t - done
-- Check size of u_int16_t
-- Check size of u_int16_t - done
-- Check size of int32_t
-- Check size of int32_t - done
-- Check size of uint32_t
-- Check size of uint32_t - done
-- Check size of u_int32_t
-- Check size of u_int32_t - done
-- Check size of int64_t
-- Check size of int64_t - done
-- Check size of short
-- Check size of short - done
-- Check size of int
-- Check size of int - done
-- Check size of long
-- Check size of long - done
-- Check size of long long
-- Check size of long long - done
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/external_libogg-prefix/src/external_libogg-build
[ 75%] Performing build step for 'external_libogg'
[ 14%] Building C object CMakeFiles/test_framing.dir/src/framing.c.o
[ 28%] Linking C executable test_framing
[ 28%] Built target test_framing
[ 42%] Building C object CMakeFiles/test_bitwise.dir/src/bitwise.c.o
[ 57%] Linking C executable test_bitwise
[ 57%] Built target test_bitwise
[ 71%] Building C object CMakeFiles/ogg.dir/src/bitwise.c.o
[ 85%] Building C object CMakeFiles/ogg.dir/src/framing.c.o
[100%] Linking C static library libogg.a
[100%] Built target ogg
[ 87%] Performing install step for 'external_libogg'
Consolidate compiler generated dependencies of target test_framing
[ 28%] Built target test_framing
Consolidate compiler generated dependencies of target test_bitwise
[ 57%] Built target test_bitwise
Consolidate compiler generated dependencies of target ogg
[100%] Built target ogg
Install the project...
-- Install configuration: ""
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/lib/libogg.a
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/include/ogg/config_types.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/include/ogg/ogg.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/include/ogg/os_types.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/lib/cmake/Ogg/OggTargets.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/lib/cmake/Ogg/OggTargets-noconfig.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/lib/cmake/Ogg/OggConfig.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/lib/cmake/Ogg/OggConfigVersion.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/lib/pkgconfig/ogg.pc
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/framing.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/index.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/oggstream.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/ogg-multiplex.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/fish_xiph_org.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/multiplex1.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/packets.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/pages.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/stream.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/vorbisword2.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/white-ogg.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/white-xifish.png
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/rfc3533.txt
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/rfc5334.txt
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/skeleton.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_eos.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_bos.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_pageout_fill.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_init.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_packetin.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_packet.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_writecopy.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_reset_serialno.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/bitpacking.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_version.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_writecheck.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_pageseek.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/general.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_wrote.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/Makefile.am
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_buffer.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_pageout.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_get_buffer.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_iovecin.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_write.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_clear.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_reset.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_buffer.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_writealign.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_reset.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_look.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_checksum_set.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_bits.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/style.css
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_reset.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/reference.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_packetpeek.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_check.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/decoding.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_adv1.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_flush_fill.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_read1.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_granulepos.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_readinit.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_init.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_serialno.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_pagein.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_state.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_check.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/datastructures.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_state.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_continued.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_bytes.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_packetout.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_packet_clear.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_packets.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_pageout.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_look1.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_eos.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_clear.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_read.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_destroy.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_stream_flush.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_adv.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_writetrunc.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_writeinit.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/encoding.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_page_pageno.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/oggpack_writeclear.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_sync_destroy.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/overview.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/index.html
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libogg/x86_64/share/doc/libogg/html/libogg/ogg_iovec_t.html
[100%] Completed 'external_libogg'
[100%] Built target external_libogg
Ogg for x86_64 build done.
-- ANDROID_PLATFORM not set. Defaulting to minimum supported version
16.
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out
[ 12%] Creating directories for 'external_libopus'
[ 25%] Performing download step (git clone) for 'external_libopus'
Cloning into 'src'...
HEAD is now at ad8fe90d Fix typo in _FORTIFY_SOURCE define.
[ 37%] Performing update step for 'external_libopus'
[ 50%] No patch step for 'external_libopus'
[ 62%] Performing configure step for 'external_libopus'
-- Opus library version: 0.8.0
-- Found Git: /usr/bin/git (found version "2.34.1") 
fatal: not a git repository (or any of the parent directories): .git
-- Opus package version: 0
-- Opus project version: 0
-- ANDROID_PLATFORM not set. Defaulting to minimum supported version
16.
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- CMAKE_C_FLAGS: -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security 
-- Looking for floor in m
-- Looking for floor in m - found
-- Looking for cpuid.h
-- Looking for cpuid.h - found
-- Check SIMD support by compiler
-- Looking for xmmintrin.h
-- Looking for xmmintrin.h - found
-- Performing Test SSE1_SUPPORTED
-- Performing Test SSE1_SUPPORTED - Success
-- Looking for emmintrin.h
-- Looking for emmintrin.h - found
-- Performing Test SSE2_SUPPORTED
-- Performing Test SSE2_SUPPORTED - Success
-- Looking for smmintrin.h
-- Looking for smmintrin.h - found
-- Performing Test SSE4_1_SUPPORTED
-- Performing Test SSE4_1_SUPPORTED - Success
-- Looking for immintrin.h
-- Looking for immintrin.h - found
-- Performing Test AVX_SUPPORTED
-- Performing Test AVX_SUPPORTED - Success
-- Performing Test STACK_PROTECTION_STRONG_SUPPORTED
-- Performing Test STACK_PROTECTION_STRONG_SUPPORTED - Success
-- The following features have been enabled:

 * STACK_PROTECTOR, Use stack protection
 * FLOAT_API, compile with the floating point API (for machines with float library)
 * INSTALL_PKG_CONFIG_MODULE, install PkgConfig module
 * INSTALL_CMAKE_CONFIG_MODULE, install CMake package config module
 * X86_MAY_HAVE_SSE, does runtime check for SSE1 support
 * X86_MAY_HAVE_SSE2, does runtime check for SSE2 support
 * X86_MAY_HAVE_SSE4_1, does runtime check for SSE4_1 support
 * X86_MAY_HAVE_AVX, does runtime check for AVX support
 * X86_PRESUME_SSE, assume target CPU has SSE1 support
 * X86_PRESUME_SSE2, assume target CPU has SSE2 support

-- The following REQUIRED packages have been found:

 * Git, fast, scalable, distributed revision control system, <https://git-scm.com/>
   required to set up package version

-- The following features have been disabled:

 * USE_ALLOCA, Use alloca for stack arrays (on non-C99 compilers)
 * CUSTOM_MODES, Enable non-Opus modes, e.g. 44.1 kHz & 2^n frames
 * BUILD_PROGRAMS, Build programs
 * FIXED_POINT, compile as fixed-point (for machines without a fast enough FPU)
 * X86_PRESUME_SSE4_1, assume target CPU has SSE4_1 support
 * X86_PRESUME_AVX, assume target CPU has AVX support

-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/external_libopus-prefix/src/external_libopus-build
[ 75%] Performing build step for 'external_libopus'
[  0%] Building C object CMakeFiles/opus.dir/src/opus.c.o
[  1%] Building C object CMakeFiles/opus.dir/src/opus_decoder.c.o
/home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/libopus/src/src/opus_decoder.c:37:10: warning: You appear to be compiling without optimization, if so opus will be very slow. [-W#pragma-messages]
# pragma message "You appear to be compiling without optimization, if so opus will be very slow."
         ^
1 warning generated.
[  2%] Building C object CMakeFiles/opus.dir/src/opus_encoder.c.o
[  2%] Building C object CMakeFiles/opus.dir/src/opus_multistream.c.o
[  3%] Building C object CMakeFiles/opus.dir/src/opus_multistream_encoder.c.o
[  4%] Building C object CMakeFiles/opus.dir/src/opus_multistream_decoder.c.o
[  4%] Building C object CMakeFiles/opus.dir/src/repacketizer.c.o
[  5%] Building C object CMakeFiles/opus.dir/src/opus_projection_encoder.c.o
[  6%] Building C object CMakeFiles/opus.dir/src/opus_projection_decoder.c.o
[  6%] Building C object CMakeFiles/opus.dir/src/mapping_matrix.c.o
[  7%] Building C object CMakeFiles/opus.dir/src/analysis.c.o
[  8%] Building C object CMakeFiles/opus.dir/src/mlp.c.o
[  8%] Building C object CMakeFiles/opus.dir/src/mlp_data.c.o
[  9%] Building C object CMakeFiles/opus.dir/silk/CNG.c.o
[ 10%] Building C object CMakeFiles/opus.dir/silk/code_signs.c.o
[ 10%] Building C object CMakeFiles/opus.dir/silk/init_decoder.c.o
[ 11%] Building C object CMakeFiles/opus.dir/silk/decode_core.c.o
[ 12%] Building C object CMakeFiles/opus.dir/silk/decode_frame.c.o
[ 12%] Building C object CMakeFiles/opus.dir/silk/decode_parameters.c.o
[ 13%] Building C object CMakeFiles/opus.dir/silk/decode_indices.c.o
[ 14%] Building C object CMakeFiles/opus.dir/silk/decode_pulses.c.o
[ 14%] Building C object CMakeFiles/opus.dir/silk/decoder_set_fs.c.o
[ 15%] Building C object CMakeFiles/opus.dir/silk/dec_API.c.o
[ 16%] Building C object CMakeFiles/opus.dir/silk/enc_API.c.o
[ 16%] Building C object CMakeFiles/opus.dir/silk/encode_indices.c.o
[ 17%] Building C object CMakeFiles/opus.dir/silk/encode_pulses.c.o
[ 18%] Building C object CMakeFiles/opus.dir/silk/gain_quant.c.o
[ 18%] Building C object CMakeFiles/opus.dir/silk/interpolate.c.o
[ 19%] Building C object CMakeFiles/opus.dir/silk/LP_variable_cutoff.c.o
[ 20%] Building C object CMakeFiles/opus.dir/silk/NLSF_decode.c.o
[ 20%] Building C object CMakeFiles/opus.dir/silk/NSQ.c.o
[ 21%] Building C object CMakeFiles/opus.dir/silk/NSQ_del_dec.c.o
[ 22%] Building C object CMakeFiles/opus.dir/silk/PLC.c.o
[ 22%] Building C object CMakeFiles/opus.dir/silk/shell_coder.c.o
[ 23%] Building C object CMakeFiles/opus.dir/silk/tables_gain.c.o
[ 24%] Building C object CMakeFiles/opus.dir/silk/tables_LTP.c.o
[ 24%] Building C object CMakeFiles/opus.dir/silk/tables_NLSF_CB_NB_MB.c.o
[ 25%] Building C object CMakeFiles/opus.dir/silk/tables_NLSF_CB_WB.c.o
[ 26%] Building C object CMakeFiles/opus.dir/silk/tables_other.c.o
[ 26%] Building C object CMakeFiles/opus.dir/silk/tables_pitch_lag.c.o
[ 27%] Building C object CMakeFiles/opus.dir/silk/tables_pulses_per_block.c.o
[ 28%] Building C object CMakeFiles/opus.dir/silk/VAD.c.o
[ 28%] Building C object CMakeFiles/opus.dir/silk/control_audio_bandwidth.c.o
[ 29%] Building C object CMakeFiles/opus.dir/silk/quant_LTP_gains.c.o
[ 30%] Building C object CMakeFiles/opus.dir/silk/VQ_WMat_EC.c.o
[ 30%] Building C object CMakeFiles/opus.dir/silk/HP_variable_cutoff.c.o
[ 31%] Building C object CMakeFiles/opus.dir/silk/NLSF_encode.c.o
[ 32%] Building C object CMakeFiles/opus.dir/silk/NLSF_VQ.c.o
[ 32%] Building C object CMakeFiles/opus.dir/silk/NLSF_unpack.c.o
[ 33%] Building C object CMakeFiles/opus.dir/silk/NLSF_del_dec_quant.c.o
[ 34%] Building C object CMakeFiles/opus.dir/silk/process_NLSFs.c.o
[ 34%] Building C object CMakeFiles/opus.dir/silk/stereo_LR_to_MS.c.o
[ 35%] Building C object CMakeFiles/opus.dir/silk/stereo_MS_to_LR.c.o
[ 36%] Building C object CMakeFiles/opus.dir/silk/check_control_input.c.o
[ 36%] Building C object CMakeFiles/opus.dir/silk/control_SNR.c.o
[ 37%] Building C object CMakeFiles/opus.dir/silk/init_encoder.c.o
[ 38%] Building C object CMakeFiles/opus.dir/silk/control_codec.c.o
[ 38%] Building C object CMakeFiles/opus.dir/silk/A2NLSF.c.o
[ 39%] Building C object CMakeFiles/opus.dir/silk/ana_filt_bank_1.c.o
[ 40%] Building C object CMakeFiles/opus.dir/silk/biquad_alt.c.o
[ 40%] Building C object CMakeFiles/opus.dir/silk/bwexpander_32.c.o
[ 41%] Building C object CMakeFiles/opus.dir/silk/bwexpander.c.o
[ 42%] Building C object CMakeFiles/opus.dir/silk/debug.c.o
[ 42%] Building C object CMakeFiles/opus.dir/silk/decode_pitch.c.o
[ 43%] Building C object CMakeFiles/opus.dir/silk/inner_prod_aligned.c.o
[ 44%] Building C object CMakeFiles/opus.dir/silk/lin2log.c.o
[ 44%] Building C object CMakeFiles/opus.dir/silk/log2lin.c.o
[ 45%] Building C object CMakeFiles/opus.dir/silk/LPC_analysis_filter.c.o
[ 46%] Building C object CMakeFiles/opus.dir/silk/LPC_inv_pred_gain.c.o
[ 46%] Building C object CMakeFiles/opus.dir/silk/table_LSF_cos.c.o
[ 47%] Building C object CMakeFiles/opus.dir/silk/NLSF2A.c.o
[ 48%] Building C object CMakeFiles/opus.dir/silk/NLSF_stabilize.c.o
[ 48%] Building C object CMakeFiles/opus.dir/silk/NLSF_VQ_weights_laroia.c.o
[ 49%] Building C object CMakeFiles/opus.dir/silk/pitch_est_tables.c.o
[ 50%] Building C object CMakeFiles/opus.dir/silk/resampler.c.o
[ 51%] Building C object CMakeFiles/opus.dir/silk/resampler_down2_3.c.o
[ 51%] Building C object CMakeFiles/opus.dir/silk/resampler_down2.c.o
[ 52%] Building C object CMakeFiles/opus.dir/silk/resampler_private_AR2.c.o
[ 53%] Building C object CMakeFiles/opus.dir/silk/resampler_private_down_FIR.c.o
[ 53%] Building C object CMakeFiles/opus.dir/silk/resampler_private_IIR_FIR.c.o
[ 54%] Building C object CMakeFiles/opus.dir/silk/resampler_private_up2_HQ.c.o
[ 55%] Building C object CMakeFiles/opus.dir/silk/resampler_rom.c.o
[ 55%] Building C object CMakeFiles/opus.dir/silk/sigm_Q15.c.o
[ 56%] Building C object CMakeFiles/opus.dir/silk/sort.c.o
[ 57%] Building C object CMakeFiles/opus.dir/silk/sum_sqr_shift.c.o
[ 57%] Building C object CMakeFiles/opus.dir/silk/stereo_decode_pred.c.o
[ 58%] Building C object CMakeFiles/opus.dir/silk/stereo_encode_pred.c.o
[ 59%] Building C object CMakeFiles/opus.dir/silk/stereo_find_predictor.c.o
[ 59%] Building C object CMakeFiles/opus.dir/silk/stereo_quant_pred.c.o
[ 60%] Building C object CMakeFiles/opus.dir/silk/LPC_fit.c.o
[ 61%] Building C object CMakeFiles/opus.dir/celt/bands.c.o
[ 61%] Building C object CMakeFiles/opus.dir/celt/celt.c.o
[ 62%] Building C object CMakeFiles/opus.dir/celt/celt_encoder.c.o
[ 63%] Building C object CMakeFiles/opus.dir/celt/celt_decoder.c.o
[ 63%] Building C object CMakeFiles/opus.dir/celt/cwrs.c.o
[ 64%] Building C object CMakeFiles/opus.dir/celt/entcode.c.o
[ 65%] Building C object CMakeFiles/opus.dir/celt/entdec.c.o
[ 65%] Building C object CMakeFiles/opus.dir/celt/entenc.c.o
[ 66%] Building C object CMakeFiles/opus.dir/celt/kiss_fft.c.o
[ 67%] Building C object CMakeFiles/opus.dir/celt/laplace.c.o
[ 67%] Building C object CMakeFiles/opus.dir/celt/mathops.c.o
[ 68%] Building C object CMakeFiles/opus.dir/celt/mdct.c.o
[ 69%] Building C object CMakeFiles/opus.dir/celt/modes.c.o
[ 69%] Building C object CMakeFiles/opus.dir/celt/pitch.c.o
[ 70%] Building C object CMakeFiles/opus.dir/celt/celt_lpc.c.o
[ 71%] Building C object CMakeFiles/opus.dir/celt/quant_bands.c.o
[ 71%] Building C object CMakeFiles/opus.dir/celt/rate.c.o
[ 72%] Building C object CMakeFiles/opus.dir/celt/vq.c.o
[ 73%] Building C object CMakeFiles/opus.dir/silk/float/apply_sine_window_FLP.c.o
[ 73%] Building C object CMakeFiles/opus.dir/silk/float/corrMatrix_FLP.c.o
[ 74%] Building C object CMakeFiles/opus.dir/silk/float/encode_frame_FLP.c.o
[ 75%] Building C object CMakeFiles/opus.dir/silk/float/find_LPC_FLP.c.o
[ 75%] Building C object CMakeFiles/opus.dir/silk/float/find_LTP_FLP.c.o
[ 76%] Building C object CMakeFiles/opus.dir/silk/float/find_pitch_lags_FLP.c.o
[ 77%] Building C object CMakeFiles/opus.dir/silk/float/find_pred_coefs_FLP.c.o
[ 77%] Building C object CMakeFiles/opus.dir/silk/float/LPC_analysis_filter_FLP.c.o
[ 78%] Building C object CMakeFiles/opus.dir/silk/float/LTP_analysis_filter_FLP.c.o
[ 79%] Building C object CMakeFiles/opus.dir/silk/float/LTP_scale_ctrl_FLP.c.o
[ 79%] Building C object CMakeFiles/opus.dir/silk/float/noise_shape_analysis_FLP.c.o
[ 80%] Building C object CMakeFiles/opus.dir/silk/float/process_gains_FLP.c.o
[ 81%] Building C object CMakeFiles/opus.dir/silk/float/regularize_correlations_FLP.c.o
[ 81%] Building C object CMakeFiles/opus.dir/silk/float/residual_energy_FLP.c.o
[ 82%] Building C object CMakeFiles/opus.dir/silk/float/warped_autocorrelation_FLP.c.o
[ 83%] Building C object CMakeFiles/opus.dir/silk/float/wrappers_FLP.c.o
[ 83%] Building C object CMakeFiles/opus.dir/silk/float/autocorrelation_FLP.c.o
[ 84%] Building C object CMakeFiles/opus.dir/silk/float/burg_modified_FLP.c.o
[ 85%] Building C object CMakeFiles/opus.dir/silk/float/bwexpander_FLP.c.o
[ 85%] Building C object CMakeFiles/opus.dir/silk/float/energy_FLP.c.o
[ 86%] Building C object CMakeFiles/opus.dir/silk/float/inner_product_FLP.c.o
[ 87%] Building C object CMakeFiles/opus.dir/silk/float/k2a_FLP.c.o
[ 87%] Building C object CMakeFiles/opus.dir/silk/float/LPC_inv_pred_gain_FLP.c.o
[ 88%] Building C object CMakeFiles/opus.dir/silk/float/pitch_analysis_core_FLP.c.o
[ 89%] Building C object CMakeFiles/opus.dir/silk/float/scale_copy_vector_FLP.c.o
[ 89%] Building C object CMakeFiles/opus.dir/silk/float/scale_vector_FLP.c.o
[ 90%] Building C object CMakeFiles/opus.dir/silk/float/schur_FLP.c.o
[ 91%] Building C object CMakeFiles/opus.dir/silk/float/sort_FLP.c.o
[ 91%] Building C object CMakeFiles/opus.dir/celt/x86/x86cpu.c.o
[ 92%] Building C object CMakeFiles/opus.dir/celt/x86/x86_celt_map.c.o
[ 93%] Building C object CMakeFiles/opus.dir/celt/x86/pitch_sse.c.o
[ 93%] Building C object CMakeFiles/opus.dir/celt/x86/pitch_sse2.c.o
[ 94%] Building C object CMakeFiles/opus.dir/celt/x86/vq_sse2.c.o
[ 95%] Building C object CMakeFiles/opus.dir/celt/x86/celt_lpc_sse4_1.c.o
[ 95%] Building C object CMakeFiles/opus.dir/celt/x86/pitch_sse4_1.c.o
[ 96%] Building C object CMakeFiles/opus.dir/silk/x86/NSQ_sse4_1.c.o
[ 97%] Building C object CMakeFiles/opus.dir/silk/x86/NSQ_del_dec_sse4_1.c.o
[ 97%] Building C object CMakeFiles/opus.dir/silk/x86/x86_silk_map.c.o
[ 98%] Building C object CMakeFiles/opus.dir/silk/x86/VAD_sse4_1.c.o
[ 99%] Building C object CMakeFiles/opus.dir/silk/x86/VQ_WMat_EC_sse4_1.c.o
[100%] Linking C static library libopus.a
[100%] Built target opus
[ 87%] Performing install step for 'external_libopus'
Consolidate compiler generated dependencies of target opus
[100%] Built target opus
Install the project...
-- Install configuration: ""
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/lib/libopus.a
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/include/opus/opus.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/include/opus/opus_custom.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/include/opus/opus_defines.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/include/opus/opus_multistream.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/include/opus/opus_projection.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/include/opus/opus_types.h
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/lib/pkgconfig/opus.pc
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/lib/cmake/Opus/OpusTargets.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/lib/cmake/Opus/OpusTargets-noconfig.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/lib/cmake/Opus/OpusConfig.cmake
-- Installing: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/libopus/x86_64/lib/cmake/Opus/OpusConfigVersion.cmake
[100%] Completed 'external_libopus'
[100%] Built target external_libopus
Opus for x86_64 build done.
CMake Warning (dev) in CMakeLists.txt:
  No project() command is present.  The top-level CMakeLists.txt file must
  contain a literal, direct call to the project() command.  Add a line of
  code such as

    project(ProjectName)

  near the top of the file, but after cmake_minimum_required().

  CMake is pretending there is a "project(Project)" command on the first
  line.
This warning is for project developers.  Use -Wno-dev to suppress it.

-- ANDROID_PLATFORM not set. Defaulting to minimum supported version
16.
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out
[ 12%] Creating directories for 'external_opus_tools'
[ 25%] Performing download step (git clone) for 'external_opus_tools'
Cloning into 'src'...
HEAD is now at 14f650f Initial version of proper multichannel WAV output.
[ 37%] Performing update step for 'external_opus_tools'
[ 50%] No patch step for 'external_opus_tools'
[ 62%] Performing configure step for 'external_opus_tools'
current dir is /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/build/
CMake Warning (dev) in CMakeLists.txt:
  No project() command is present.  The top-level CMakeLists.txt file must
  contain a literal, direct call to the project() command.  Add a line of
  code such as

    project(ProjectName)

  near the top of the file, but after cmake_minimum_required().

  CMake is pretending there is a "project(Project)" command on the first
  line.
This warning is for project developers.  Use -Wno-dev to suppress it.

-- ANDROID_PLATFORM not set. Defaulting to minimum supported version
16.
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /home/<USER>/Android/Sdk/ndk-bundle/21.4.7075529/toolchains/llvm/prebuilt/linux-x86_64/bin/clang++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/Documents/live-transcribe-speech-engine-master/app/third_party/out/build/opus_tools/x86_64/lib
[ 75%] Performing build step for 'external_opus_tools'
[ 50%] Building C object CMakeFiles/opus_header.dir/src/opus_header.c.o
[100%] Linking C static library libopus_header.a
[100%] Built target opus_header
[ 87%] Performing install step for 'external_opus_tools'
[100%] Completed 'external_opus_tools'
[100%] Built target external_opus_tools
Opus-tools for x86_64 build done.

> Configure project :app
WARNING:: This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement
This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement
WARNING:: This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement
This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement

> Task :wrapper UP-TO-DATE

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.2/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 983ms
1 actionable task: 1 up-to-date

> Configure project :app
WARNING:: This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement
This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement
WARNING:: This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement
This app only has 32-bit [armeabi-v7a] native libraries. Beginning August 1, 2019 Google Play store requires that all apps that include native libraries must provide 64-bit versions. For more information, visit https://g.co/64-bit-requirement

> Task :app:preBuild UP-TO-DATE
> Task :app:preArmeabiDebugBuild UP-TO-DATE
> Task :app:compileArmeabiDebugAidl NO-SOURCE
> Task :app:compileArmeabiDebugRenderscript NO-SOURCE
> Task :app:generateArmeabiDebugBuildConfig UP-TO-DATE
> Task :app:extractArmeabiDebugProto UP-TO-DATE
> Task :app:extractArmeabiProto UP-TO-DATE
> Task :app:extractDebugProto UP-TO-DATE
> Task :app:extractIncludeArmeabiDebugProto UP-TO-DATE
> Task :app:extractProto UP-TO-DATE
> Task :app:generateArmeabiDebugProto UP-TO-DATE
> Task :app:javaPreCompileArmeabiDebug UP-TO-DATE
> Task :app:checkArmeabiDebugAarMetadata UP-TO-DATE
> Task :app:generateArmeabiDebugResValues UP-TO-DATE
> Task :app:generateArmeabiDebugResources UP-TO-DATE
> Task :app:mergeArmeabiDebugResources UP-TO-DATE
> Task :app:createArmeabiDebugCompatibleScreenManifests
> Task :app:extractDeepLinksArmeabiDebug UP-TO-DATE

> Task :app:processArmeabiDebugMainManifest
[com.android.support:animated-vector-drawable:28.0.0] /home/<USER>/.gradle/caches/transforms-3/bd82ab22a49a46ea10a7dbadd52e76d4/transformed/animated-vector-drawable-28.0.0/AndroidManifest.xml Warning:
	Package name 'android.support.graphics.drawable' used in: com.android.support:animated-vector-drawable:28.0.0, com.android.support:support-vector-drawable:28.0.0.

> Task :app:processArmeabiDebugManifest
> Task :app:generateJsonModelArmeabiDebug

> Task :app:externalNativeBuildArmeabiDebug
Build multiple targets ogg_opus_encoder_armeabi-v7a ogg_opus_encoder_tool_armeabi-v7a
ninja: Entering directory `/home/<USER>/Documents/live-transcribe-speech-engine-master/app/.cxx/cmake/armeabiDebug/armeabi-v7a'

> Task :app:mergeArmeabiDebugNativeDebugMetadata NO-SOURCE
> Task :app:mergeArmeabiDebugShaders UP-TO-DATE
> Task :app:compileArmeabiDebugShaders NO-SOURCE
> Task :app:generateArmeabiDebugAssets UP-TO-DATE
> Task :app:mergeArmeabiDebugAssets UP-TO-DATE
> Task :app:compressArmeabiDebugAssets UP-TO-DATE
> Task :app:processArmeabiDebugJavaRes NO-SOURCE
> Task :app:mergeArmeabiDebugJavaResource UP-TO-DATE
> Task :app:checkArmeabiDebugDuplicateClasses UP-TO-DATE
> Task :app:desugarArmeabiDebugFileDependencies
> Task :app:processArmeabiDebugManifestForPackage
> Task :app:mergeLibDexArmeabiDebug
> Task :app:mergeArmeabiDebugJniLibFolders UP-TO-DATE
> Task :app:mergeArmeabiDebugNativeLibs UP-TO-DATE
> Task :app:stripArmeabiDebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningArmeabiDebug UP-TO-DATE
> Task :app:writeArmeabiDebugAppMetadata UP-TO-DATE
> Task :app:writeArmeabiDebugSigningConfigVersions
> Task :app:preX86DebugBuild UP-TO-DATE
> Task :app:compileX86DebugAidl NO-SOURCE
> Task :app:compileX86DebugRenderscript NO-SOURCE
> Task :app:generateX86DebugBuildConfig UP-TO-DATE
> Task :app:extractIncludeX86DebugProto UP-TO-DATE
> Task :app:extractX86DebugProto UP-TO-DATE
> Task :app:extractX86Proto UP-TO-DATE
> Task :app:generateX86DebugProto UP-TO-DATE
> Task :app:javaPreCompileX86Debug UP-TO-DATE
> Task :app:checkX86DebugAarMetadata UP-TO-DATE
> Task :app:generateX86DebugResValues UP-TO-DATE
> Task :app:generateX86DebugResources UP-TO-DATE
> Task :app:mergeX86DebugResources UP-TO-DATE
> Task :app:createX86DebugCompatibleScreenManifests
> Task :app:extractDeepLinksX86Debug UP-TO-DATE

> Task :app:processX86DebugMainManifest
[com.android.support:animated-vector-drawable:28.0.0] /home/<USER>/.gradle/caches/transforms-3/bd82ab22a49a46ea10a7dbadd52e76d4/transformed/animated-vector-drawable-28.0.0/AndroidManifest.xml Warning:
	Package name 'android.support.graphics.drawable' used in: com.android.support:animated-vector-drawable:28.0.0, com.android.support:support-vector-drawable:28.0.0.

> Task :app:processX86DebugManifest
> Task :app:generateJsonModelX86Debug

> Task :app:externalNativeBuildX86Debug
Build multiple targets ogg_opus_encoder_tool_x86_64 ogg_opus_encoder_x86_64
ninja: Entering directory `/home/<USER>/Documents/live-transcribe-speech-engine-master/app/.cxx/cmake/x86Debug/x86_64'

> Task :app:mergeX86DebugNativeDebugMetadata NO-SOURCE
> Task :app:mergeX86DebugShaders UP-TO-DATE
> Task :app:compileX86DebugShaders NO-SOURCE
> Task :app:generateX86DebugAssets UP-TO-DATE
> Task :app:mergeX86DebugAssets UP-TO-DATE
> Task :app:compressX86DebugAssets UP-TO-DATE
> Task :app:checkX86DebugDuplicateClasses UP-TO-DATE
> Task :app:desugarX86DebugFileDependencies
> Task :app:processX86DebugManifestForPackage
> Task :app:processArmeabiDebugResources
> Task :app:mergeExtDexArmeabiDebug

> Task :app:compileArmeabiDebugJavaWithJavac
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.

> Task :app:processX86DebugResources
> Task :app:compileArmeabiDebugSources
> Task :app:mergeExtDexX86Debug
> Task :app:dexBuilderArmeabiDebug
> Task :app:mergeProjectDexArmeabiDebug

> Task :app:compileX86DebugJavaWithJavac
Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.

> Task :app:compileX86DebugSources
> Task :app:mergeLibDexX86Debug

> Task :app:packageArmeabiDebug
There are no .so files available to package in the APK for x86_64.
There are no .so files available to package in the APK for x86.

> Task :app:dexBuilderX86Debug
> Task :app:processX86DebugJavaRes NO-SOURCE
> Task :app:mergeX86DebugJavaResource UP-TO-DATE
> Task :app:mergeX86DebugJniLibFolders UP-TO-DATE
> Task :app:mergeX86DebugNativeLibs UP-TO-DATE
> Task :app:stripX86DebugDebugSymbols UP-TO-DATE
> Task :app:validateSigningX86Debug UP-TO-DATE
> Task :app:writeX86DebugAppMetadata UP-TO-DATE
> Task :app:writeX86DebugSigningConfigVersions
> Task :app:assembleArmeabiDebug
> Task :app:mergeProjectDexX86Debug

> Task :app:packageX86Debug
There are no .so files available to package in the APK for armeabi-v7a.
There are no .so files available to package in the APK for x86.

> Task :app:assembleX86Debug
> Task :app:assembleDebug

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.2/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 29s
72 actionable tasks: 30 executed, 42 up-to-date
