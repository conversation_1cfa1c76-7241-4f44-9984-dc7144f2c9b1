// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url "https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java-util"
        }
    }
    dependencies {
        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.9.1'
        classpath 'com.android.tools.build:gradle:4.2.2'
        classpath 'com.google.cloud:google-cloud-speech:0.84.0-beta'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }

}

allprojects {
    repositories {
        google()
        mavenCentral()
//        mavenCentral()

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}



