# .aar Integration Troubleshooting Guide

This guide helps resolve common issues when integrating the ASL Android Input library as an .aar file into Flutter packages.

## Common Issues and Solutions

### 1. "Class not found" or "NoClassDefFoundError"

**Problem**: The Flutter app can't find classes from the .aar file.

**Solutions**:
- Ensure the .aar file is placed in the correct location: `android/libs/`
- Verify the build.gradle includes: `implementation files('libs/asl_v1.0.x.aar')`
- Check that the package name in the plugin registration matches the .aar

### 2. "Method channel not found" or "MissingPluginException"

**Problem**: Flutter can't communicate with the native Android code.

**Solutions**:
- Ensure `AslFlutterInputPlugin.java` is in the Flutter package, not in the .aar
- Verify the plugin is properly registered in `pubspec.yaml`:
  ```yaml
  flutter:
    plugin:
      platforms:
        android:
          package: com.citrus.audio
          pluginClass: AslFlutterInputPlugin
  ```
- Check that the method channel name matches: `com.citrus.audio/asl_flutter_input`

### 3. Dependency Conflicts

**Problem**: Build fails due to conflicting dependencies.

**Solutions**:
- Ensure Google Cloud Speech dependencies are in the Flutter package's build.gradle, not the .aar
- Use the exact dependency versions specified in the integration guide
- Add exclusions for conflicting dependencies:
  ```gradle
  implementation('com.google.cloud:google-cloud-speech:4.69.0') {
      exclude group: 'commons-codec', module: 'commons-codec'
      exclude group: 'com.google.guava', module: 'guava'
  }
  ```

### 4. "Failed to create SpeechClient" Error

**Problem**: Google Cloud Speech client initialization fails.

**Solutions**:
- Verify all required dependencies are included in the Flutter package
- Check that the service account JSON is properly formatted
- Use the diagnostic methods to troubleshoot:
  ```dart
  String report = await AslFlutterInput.getDiagnosticReport();
  print(report);
  ```

### 5. Proguard/R8 Issues

**Problem**: Release builds fail or crash due to code obfuscation.

**Solutions**:
- Add proguard rules to keep necessary classes:
  ```
  -keep class com.citrus.audio.** { *; }
  -keep class com.google.cloud.speech.** { *; }
  -keep class com.google.auth.** { *; }
  ```

## Verification Steps

### 1. Check .aar Contents
```bash
# Extract and inspect the .aar file
unzip -l asl_v1.0.x.aar
```

Verify it contains:
- `classes.jar` with your library classes
- `AndroidManifest.xml`
- Native libraries (if any)

### 2. Test Plugin Registration
```dart
// Test if the plugin is properly registered
try {
  String version = await AslFlutterInput.getVersion();
  print('Plugin version: $version');
} catch (e) {
  print('Plugin registration failed: $e');
}
```

### 3. Test Core Library Access
```dart
// Test if the core library is accessible
try {
  bool configured = await AslFlutterInput.isConfigured();
  print('Library accessible: $configured');
} catch (e) {
  print('Core library access failed: $e');
}
```

### 4. Test Authentication
```dart
// Test authentication before using transcription
try {
  await AslFlutterInput.testAuthentication(projectId, serviceAccountJson);
  print('Authentication test passed');
} catch (e) {
  print('Authentication failed: $e');
}
```

## Build Configuration Checklist

### Flutter Package build.gradle
- [ ] Includes .aar file: `implementation files('libs/asl_v1.0.x.aar')`
- [ ] Includes all Google Cloud Speech dependencies
- [ ] Uses correct dependency versions
- [ ] Excludes conflicting dependencies
- [ ] Sets correct minSdkVersion (21+)

### pubspec.yaml
- [ ] Plugin is properly registered
- [ ] Package name matches Android package
- [ ] Plugin class name is correct
- [ ] Flutter version constraints are appropriate

### Plugin Class
- [ ] Extends FlutterPlugin and implements ActivityAware
- [ ] Uses correct method channel name
- [ ] Handles all required method calls
- [ ] Properly delegates to AslAudioLibrary

## Debugging Commands

### Check Dependencies
```bash
# In your Flutter app
flutter pub deps
./gradlew app:dependencies
```

### Check Plugin Registration
```bash
# Look for plugin registration logs
adb logcat | grep "AslFlutterInputPlugin"
```

### Check Method Channel Communication
```bash
# Look for method channel logs
adb logcat | grep "MethodChannel"
```

## Migration from Source Code to .aar

If you're migrating from using the library as source code:

1. **Remove source code dependencies** from your Flutter app's build.gradle
2. **Add .aar dependency** to your Flutter package's build.gradle
3. **Move plugin class** from app to Flutter package
4. **Update import statements** to use the Flutter package
5. **Test thoroughly** with both debug and release builds

## Performance Considerations

- The .aar approach reduces build times for consuming apps
- All dependencies are resolved at the Flutter package level
- Native libraries are included in the .aar for better performance
- Method channel overhead is minimal for the API design

## Support and Debugging

When reporting issues, include:
- Flutter version and channel
- Android Gradle Plugin version
- Complete error logs from `flutter run -v`
- Output from diagnostic report method
- Build.gradle configurations for both package and app
