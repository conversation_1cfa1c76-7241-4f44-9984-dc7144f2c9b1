package com.citrus.audio;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/**
 * Flutter plugin for ASL Audio Library
 * This class handles the Flutter method channel integration and delegates to AslAudioLibrary
 */
public class AslFlutterInputPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private static final String TAG = "AslFlutterInputPlugin";
    private static final String CHANNEL = "com.citrus.audio/asl_flutter_input";

    private MethodChannel channel;
    private Context context;
    private Activity activity;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        Log.d(TAG, "Plugin attached to engine");
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL);
        channel.setMethodCallHandler(this);
        context = flutterPluginBinding.getApplicationContext();
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        Log.d(TAG, "Method call received: " + call.method);
        
        try {
            switch (call.method) {
                case "configure":
                    handleConfigure(call, result);
                    break;
                    
                case "initAudioTranscription":
                    handleInitAudioTranscription(call, result);
                    break;
                    
                case "stopListening":
                    handleStopListening(call, result);
                    break;
                    
                case "getTranscribedText":
                    handleGetTranscribedText(call, result);
                    break;
                    
                case "setLanguageCode":
                    handleSetLanguageCode(call, result);
                    break;
                    
                case "testAuthentication":
                    handleTestAuthentication(call, result);
                    break;
                    
                case "getDiagnosticReport":
                    handleGetDiagnosticReport(call, result);
                    break;
                    
                case "isConfigured":
                    result.success(AslAudioLibrary.isConfigured());
                    break;
                    
                case "isListening":
                    result.success(AslAudioLibrary.isListening());
                    break;
                    
                case "getVersion":
                    result.success(AslAudioLibrary.getVersion());
                    break;
                    
                case "reset":
                    AslAudioLibrary.reset();
                    result.success("Library reset successfully");
                    break;
                    
                default:
                    result.notImplemented();
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling method call: " + call.method, e);
            result.error("METHOD_CALL_ERROR", "Error executing " + call.method + ": " + e.getMessage(), null);
        }
    }

    private void handleConfigure(MethodCall call, Result result) {
        String projectId = call.argument("projectId");
        String serviceAccountJson = call.argument("serviceAccountJson");
        
        if (projectId == null || serviceAccountJson == null) {
            result.error("INVALID_ARGUMENTS", "projectId and serviceAccountJson are required", null);
            return;
        }
        
        try {
            AslAudioLibrary.configure(projectId, serviceAccountJson);
            result.success("Configuration successful");
        } catch (IllegalArgumentException e) {
            result.error("CONFIGURATION_ERROR", e.getMessage(), null);
        }
    }

    private void handleInitAudioTranscription(MethodCall call, Result result) {
        if (activity == null) {
            result.error("NO_ACTIVITY", "Activity context is required for audio transcription", null);
            return;
        }
        
        try {
            AslAudioLibrary.initAudioTranscription(activity);
            result.success("Audio transcription initialized");
        } catch (IllegalStateException e) {
            result.error("INITIALIZATION_ERROR", e.getMessage(), null);
        }
    }

    private void handleStopListening(MethodCall call, Result result) {
        AslAudioLibrary.stopListening();
        result.success("Listening stopped");
    }

    private void handleGetTranscribedText(MethodCall call, Result result) {
        String text = AslAudioLibrary.getTranscribedText();
        result.success(text);
    }

    private void handleSetLanguageCode(MethodCall call, Result result) {
        String languageCode = call.argument("languageCode");
        if (languageCode == null) {
            result.error("INVALID_ARGUMENTS", "languageCode is required", null);
            return;
        }
        
        try {
            AslAudioLibrary.setLanguageCode(languageCode);
            result.success("Language code set to: " + languageCode);
        } catch (IllegalArgumentException e) {
            result.error("INVALID_LANGUAGE_CODE", e.getMessage(), null);
        }
    }

    private void handleTestAuthentication(MethodCall call, Result result) {
        String projectId = call.argument("projectId");
        String serviceAccountJson = call.argument("serviceAccountJson");
        
        if (projectId == null || serviceAccountJson == null) {
            result.error("INVALID_ARGUMENTS", "projectId and serviceAccountJson are required for testing", null);
            return;
        }
        
        boolean authTestResult = AslAudioLibrary.testAuthentication(projectId, serviceAccountJson);
        if (authTestResult) {
            result.success("Authentication test passed");
        } else {
            String diagnosticReport = AslAudioLibrary.getDiagnosticReport();
            result.error("AUTH_TEST_FAILED", "Authentication test failed", diagnosticReport);
        }
    }

    private void handleGetDiagnosticReport(MethodCall call, Result result) {
        String report = AslAudioLibrary.getDiagnosticReport();
        result.success(report);
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        Log.d(TAG, "Plugin detached from engine");
        channel.setMethodCallHandler(null);
        channel = null;
        context = null;
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        Log.d(TAG, "Plugin attached to activity");
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        Log.d(TAG, "Plugin detached from activity for config changes");
        activity = null;
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        Log.d(TAG, "Plugin reattached to activity for config changes");
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        Log.d(TAG, "Plugin detached from activity");
        activity = null;
    }
}
