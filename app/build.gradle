apply plugin: 'com.android.library'
apply plugin: 'com.google.protobuf'

android {
    compileSdkVersion 33
    defaultConfig {
//        Comment out applicationId when building as library
//       applicationId "com.citrus.audio"
       namespace "com.citrus.audio"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        externalNativeBuild {

            cmake {
                '-DANDROID_STL=c++_static'
            }
        }
        multiDexEnabled true


    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            multiDexEnabled true

        }
    }

    sourceSets {
        main {
            java {
                srcDir 'src/main/java'
            }
            proto {
                srcDir 'src/main/proto'
            }
        }
    }

    externalNativeBuild {
        cmake {
            path 'CMakeLists.txt'
        }
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/*.SF'
        exclude 'META-INF/*.DSA'
        exclude 'META-INF/*.RSA'
        exclude 'META-INF/MANIFEST.MF'
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
    
    compileOptions {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
    }

    // Configure D8 to handle newer dependencies
    dexOptions {
        javaMaxHeapSize "4g"
        preDexLibraries = false
    }

    flavorDimensions 'abi'
//    productFlavors {
//        arm7 {
//            ndk {
//                abiFilter 'armeabi-v7a'
//            }
//        }
//        arm8 {
//            ndk {
//                abiFilter 'arm64-v8a'
//            }
//        }
//        x86 {
//            ndk {
//                abiFilter 'x86'
//            }
//        }
//        x86_64 {
//            ndk {
//                abiFilter 'x86_64'
//            }
//        }
//    }

    productFlavors {

        x86 {
            ndk {
                abiFilter 'x86_64' // Include this line
            }
        }
        armeabi {
            ndk {
                abiFilter 'armeabi-v7a'
            }
        }

    }

    // Split APK Start here
    splits {
        abi {
            enable true
            reset()
            include 'x86', 'x86_64', 'armeabi-v7a'
            universalApk true
        }
    }



}

// Force resolution of conflicting dependencies
configurations.all {
    resolutionStrategy {
        force 'commons-codec:commons-codec:1.15'
        force 'com.google.guava:guava:31.1-android'
        force 'com.google.errorprone:error_prone_annotations:2.18.0'
        force 'io.grpc:grpc-okhttp:1.71.0'
        force 'io.grpc:grpc-core:1.71.0'
        force 'io.grpc:grpc-stub:1.71.0'
        force 'com.google.auth:google-auth-library-oauth2-http:1.39.0'
        force 'com.google.auth:google-auth-library-credentials:1.39.0'
        force 'com.google.auth:google-auth-library-appengine:1.39.0'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support.constraint:constraint-layout:2.0.4'

    // Updated to use Google Cloud Speech v2 API
    implementation('com.google.cloud:google-cloud-speech:4.69.0') {
        // Exclude the problematic commons-codec version
        exclude group: 'commons-codec', module: 'commons-codec'
        // Exclude other potentially problematic dependencies
        exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
        exclude group: 'com.google.guava', module: 'guava'
    }

    // Force compatible versions for Android
    implementation 'commons-codec:commons-codec:1.15'
    implementation 'com.google.guava:guava:31.1-android'
    implementation 'com.google.errorprone:error_prone_annotations:2.18.0'

    // Ensure compatible gRPC dependencies for the v2 client library
    implementation 'io.grpc:grpc-okhttp:1.71.0'
    implementation 'io.grpc:grpc-core:1.71.0'
    implementation 'io.grpc:grpc-stub:1.71.0'

    // Google Auth library for API key authentication and mTLS support
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.39.0'
    implementation 'com.google.auth:google-auth-library-credentials:1.39.0'
    implementation 'com.google.auth:google-auth-library-appengine:1.39.0'

    implementation 'com.google.flogger:flogger:0.4'
    implementation 'com.google.flogger:flogger-system-backend:0.4'
    implementation 'com.google.protobuf:protobuf-java:3.22.3'
    implementation 'com.google.protobuf:protobuf-java-util:3.22.3'
    implementation 'joda-time:joda-time:2.9.2'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    implementation 'androidx.multidex:multidex:2.0.1'

    // Security library for encrypted shared preferences
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // Note: Flutter dependencies are NOT included in the .aar
    // They will be provided by the Flutter app that uses this library
}

protobuf {
    // Configure the protoc executable
    protoc {
        artifact = 'com.google.protobuf:protoc:3.22.3'

        generateProtoTasks {
            all().each { task ->
                task.builtins {
                    remove java
                }
                task.builtins {
                    java {}
                }
            }
        }
    }
}
