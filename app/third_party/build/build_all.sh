# TODO: should build third party libray directly by gradle. Not make them by running this script.

# Lists cpu architecture of android.
android_abi=("x86_64")

# Downloads third party source code.
if [ -z "${ANDROID_SDK_PATH}" ] ;then
   echo "\$ANDROID_SDK_PATH is empty, please export SDK path to \$ANDROID_SDK_PATH"
   exit 1
fi

NDK_PATH="${ANDROID_SDK_PATH}"/ndk/21.4.7075529;
if [ ! -d "${NDK_PATH}" ] ;then
  echo "NDK bundle not found at location ${NDK_PATH}"
  exit 1
fi

for ((i=0; i < ${#android_abi[@]}; i++));
do
  mkdir -p ../libogg/lib/${android_abi[$i]}
  mkdir -p ../libopus/lib/${android_abi[$i]}
  mkdir -p ../opus_tools/lib/${android_abi[$i]}
done

# Executes cmake command in third_party/CMakeLists.txt.in for all cpu architecture.
for ((i=0; i < ${#android_abi[@]}; i++))
do
  if [ ! -d ../out ]; then
    mkdir ../out
  fi
  cp ../CMakeLists_libogg.txt ../CMakeLists.txt
  cd ../out
  cmake -DCMAKE_TOOLCHAIN_FILE="${NDK_PATH}"/build/cmake/android.toolchain.cmake -DANDROID_ABI=${android_abi[$i]} ..
  make
  echo "Ogg for ${android_abi[$i]} build done."
  cp ../CMakeLists_libopus.txt ../CMakeLists.txt
  cd ../out
  cmake -DCMAKE_TOOLCHAIN_FILE="${NDK_PATH}"/build/cmake/android.toolchain.cmake -DANDROID_ABI=${android_abi[$i]} ..
  make
  echo "Opus for ${android_abi[$i]} build done."
  cp ../CMakeLists_opus-tools.txt ../CMakeLists.txt
  cd ../out
  cmake -DCMAKE_TOOLCHAIN_FILE="${NDK_PATH}"/build/cmake/android.toolchain.cmake -DANDROID_ABI=${android_abi[$i]} ..
  make
  echo "Opus-tools for ${android_abi[$i]} build done."
  cd ../build
  rm -rf ../out
done
