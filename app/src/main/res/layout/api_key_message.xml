<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- API Key Docs Link -->
        <TextView
            android:id="@+id/api_key_link_view"
            android:clickable="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/holo_blue_dark"
            android:textSize="14sp"
            android:paddingBottom="8dp"/>

        <!-- Project ID Input -->
        <EditText
            android:id="@+id/project_id_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter Project ID"
            android:inputType="text"
            android:paddingBottom="8dp"/>

        <!-- Service Account JSON Input -->
        <EditText
            android:id="@+id/service_account_json_input"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:hint="Paste Service Account JSON here"
            android:inputType="textMultiLine|textNoSuggestions"
            android:scrollbars="vertical"
            android:gravity="top"
            android:paddingBottom="8dp"/>

        <!-- Language selection text -->
        <TextView
            android:id="@+id/language_locale_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_language_message"
            android:paddingTop="8dp"
            android:paddingBottom="4dp"/>

        <!-- Language spinner -->
        <Spinner
            android:id="@+id/language_locale_spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </LinearLayout>
</ScrollView>
