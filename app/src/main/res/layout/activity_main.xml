<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.citrus.audio.MainActivity">

  <!-- ScrollView for the transcript text -->
  <ScrollView
      android:id="@+id/scrollview"
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:fadeScrollbars="false"
      android:scrollbars="vertical"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintBottom_toTopOf="@+id/buttonRow"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintRight_toRightOf="parent">

    <TextView
        android:id="@+id/transcript"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Say something!"
        android:textSize="@dimen/text_size"/>
  </ScrollView>

  <!-- Horizontal row for buttons -->
  <android.support.constraint.ConstraintLayout
      android:id="@+id/buttonRow"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintRight_toRightOf="parent">

    <!-- Submit Button -->
    <Button
        android:id="@+id/stopBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Submit"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/startBtn"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <!-- Start Activity Button -->
    <Button
        android:id="@+id/startBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Start New Activity"
        app:layout_constraintLeft_toRightOf="@id/stopBtn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
  </android.support.constraint.ConstraintLayout>

</android.support.constraint.ConstraintLayout>
