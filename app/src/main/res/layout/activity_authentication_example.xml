<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Title -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Google Cloud Authentication Example"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:paddingBottom="16dp" />

        <!-- Project ID Input -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Project ID:"
            android:textStyle="bold"
            android:paddingBottom="4dp" />

        <EditText
            android:id="@+id/project_id_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter your Google Cloud Project ID"
            android:inputType="text"
            android:paddingBottom="16dp" />

        <!-- Service Account JSON Input -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Service Account JSON:"
            android:textStyle="bold"
            android:paddingBottom="4dp" />

        <EditText
            android:id="@+id/service_account_json_input"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:hint="Paste your service account JSON here"
            android:inputType="textMultiLine|textNoSuggestions"
            android:scrollbars="vertical"
            android:gravity="top"
            android:paddingBottom="16dp" />

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="16dp">

            <Button
                android:id="@+id/authenticate_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Authenticate"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/load_stored_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Load Stored"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/clear_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Clear"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingBottom="16dp"
            android:visibility="gone" />

        <!-- Status Text -->
        <TextView
            android:id="@+id/status_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Ready to authenticate"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:paddingBottom="16dp" />

        <!-- Diagnostic Information -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Diagnostic Information:"
            android:textStyle="bold"
            android:paddingBottom="8dp" />

        <TextView
            android:id="@+id/diagnostic_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:background="#f0f0f0"
            android:padding="8dp"
            android:scrollbars="vertical"
            android:maxLines="20"
            android:scrollHorizontally="true" />

        <!-- Instructions -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Instructions:"
            android:textStyle="bold"
            android:paddingTop="16dp"
            android:paddingBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1. Enter your Google Cloud Project ID\n2. Paste your service account JSON\n3. Click 'Authenticate' to test the credentials\n4. Use 'Load Stored' to load previously saved credentials\n5. Use 'Clear' to clear all credentials"
            android:textSize="14sp"
            android:paddingBottom="16dp" />

        <!-- Security Notice -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Security Notice:"
            android:textStyle="bold"
            android:textColor="@android:color/holo_orange_dark"
            android:paddingBottom="4dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Credentials are stored securely using Android Keystore encryption. They are automatically cleared when the app is uninstalled."
            android:textSize="12sp"
            android:textColor="@android:color/holo_orange_dark"
            android:paddingBottom="16dp" />

    </LinearLayout>

</ScrollView>
