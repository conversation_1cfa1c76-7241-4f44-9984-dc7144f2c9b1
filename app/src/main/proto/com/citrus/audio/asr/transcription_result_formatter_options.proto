syntax = "proto2";

package asr;

import "google/protobuf/duration.proto";

option java_package = "com.citrus.audio.asr";
option java_outer_classname = "TranscriptionResultFormatterOptionsProto";
option java_multiple_files = true;

message TranscriptionResultFormatterOptions {
  // Color selection for the text (does not change background). Dark colors for
  // a black-on-white theme. Bright colors for a white-on-black theme.
  enum TextColormap {
    UNSPECIFIED_THEME = 0;
    LIGHT_THEME = 1;
    DARK_THEME = 2;
  }

  // Details on the manner in which the transcript will be colored.
  enum TranscriptColoringStyle {
    UNSPECIFIED_COLORING_STYLE = 0;  // Will do NO_COLORING.
    NO_COLORING = 1;
    COLOR_BY_UTTERANCE_LEVEL_CONFIDENCE = 2;
    COLOR_BY_WORD_LEVEL_CONFIDENCE = 3;
    COLOR_BY_SPEAKER_ID = 4;
  }

  enum SpeakerIndicationStyle {
    UNSPECIFIED_SPEAKER_INDICATION_STYLE = 0;
    NO_SPEAKER_INDICATION = 1;
    SHOW_SPEAKER_NUMBER = 2;
  }

  // Silences longer than this will cause a space to be inserted.
  optional google.protobuf.Duration extended_silence_duration_for_line_breaks =
      1;

  // Number of '\n' characters to add in the event of extended silence.
  // 1 moves to the next line, 2 leaves a blank space in between two lines,
  // and so on...
  optional int32 num_extended_silence_line_breaks = 2 [default = 0];

  // Number of '\n' characters to add in the event of language switch.
  // 1 moves to the next line, 2 leaves a blank space in between two lines, and
  // so on...
  optional int32 num_language_switch_line_breaks = 3 [default = 1];

  // Put current hypotheses in italics.
  optional bool italicize_current_hypothesis = 4 [default = false];

  // If true, use a yellow->blue colormap to indicate confidence.
  optional TranscriptColoringStyle transcript_coloring_style = 5
      [default = NO_COLORING];

  // The color theme used for the text.
  optional TextColormap text_colormap = 6 [default = DARK_THEME];

  // A label that indicates which speaker is active.
  optional SpeakerIndicationStyle speaker_indication_style = 7
      [default = NO_SPEAKER_INDICATION];
}
