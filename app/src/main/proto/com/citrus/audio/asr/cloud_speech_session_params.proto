syntax = "proto2";

package asr;

import "com/citrus/audio/asr/cloud_speech_stream_observer_params.proto";
import "com/citrus/audio/codec_and_bitrate.proto";

option java_package = "com.citrus.audio.asr";
option java_outer_classname = "CloudSpeechSessionParamsProto";
option java_multiple_files = true;

message CloudSpeechSessionParams {
  // Parameters for the observer, who converts the speech protos into
  // TranscriptionResult.
  optional CloudSpeechStreamObserverParams observer_params = 1;

  // Allows profanity to be filtered by the Cloud Speech API.
  optional bool filter_profanity = 2;

  // Details about how the audio signal should be compressed prior to sending it
  // to the server.
  message EncoderParams {
    // If the encoder isn't supported, uncompressed audio will be used. When
    // this is false, other EncoderParams fields are ignored.
    optional bool enable_encoder = 1 [default = false];
    optional audio.CodecAndBitrate codec = 2
        [default = OGG_OPUS_BITRATE_32KBPS];

    // Uses variable bitrate encoding, if available. Currently this is available
    // for OggOpus only.
    optional bool allow_vbr = 3 [default = true];
  }

  optional EncoderParams encoder_params = 3;
}
