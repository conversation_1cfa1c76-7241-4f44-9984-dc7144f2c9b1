syntax = "proto2";

package asr;

option java_package = "com.citrus.audio.asr";
option java_outer_classname = "SpeechRecognitionModelOptionsProto";
option java_multiple_files = true;

message SpeechRecognitionModelOptions {
  optional string locale = 1;  // Required.

  // Select which model to use. Not all models are necessarily available for all
  // recognition systems or locales. It is up to the individual session to warn
  // the user about availability
  enum SpecificModel {
    DICTATION_DEFAULT = 0;
    VIDEO = 1;
  }

  optional SpecificModel model = 2 [default = DICTATION_DEFAULT];

  // Words to be passed to the speech recognizer as bias. It is up to each
  // implementation to decide whether these will be used or not.
  repeated string bias_words = 3;
}
