syntax = "proto2";

package asr;

import "com/citrus/audio/speaker_id_info.proto";
import "google/protobuf/timestamp.proto";

option java_package = "com.citrus.audio.asr";
option java_outer_classname = "TranscriptionResultProto";
option java_multiple_files = true;

message TranscriptionResult {
  // An utterance level copy of the text.
  optional string text = 1;

  // Confidence for the whole utterance [0, 1].
  optional float confidence = 2;
  // The epoch time at which the utterance was started.
  optional google.protobuf.Timestamp start_timestamp = 7;
  // The epoch time at which the utterance was completed.
  optional google.protobuf.Timestamp end_timestamp = 3;

  // The identity of the speaker.
  optional audio.SpeakerIdInfo speaker_info = 5;

  // Fine-grain information about each word.
  // NOTE: the TranscriptResultFormatter may colorize the coarse-grain
  // transcript by the corresponding word information such as confidence and
  // speaker_id if fine-grain word_level_detail is not empty.
  message Word {
    optional string text = 1;
    // Confidence for just this word [0, 1].
    optional float confidence = 2;
    // An integer tag for the identity of the active speaker.
    optional audio.SpeakerIdInfo speaker_info = 3;
    // The time at which the word was started.
    optional google.protobuf.Timestamp start_timestamp = 4;
    // The time at which the word was completed.
    optional google.protobuf.Timestamp end_timestamp = 5;
  }
  // Word-level detail.
  // NOTE: Some recognizers (namely the CloudSpeech API) do not give fine-grain
  // information until results are finalized.
  repeated Word word_level_detail = 4;

  // The language code in this result.
  // See https://cloud.google.com/speech-to-text/docs/languages for more
  // details.
  // For example,
  // English (United States) : en-US
  // Chinese, Mandarin (Traditional, Taiwan) : cmn-Hant-TW
  optional string language_code = 6;
}
