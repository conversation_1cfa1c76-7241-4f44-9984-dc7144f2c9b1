syntax = "proto2";

package audio;

option java_package = "com.citrus.audio";
option java_multiple_files = true;

// In Java, (and excluding the FLAC entry) the bitrate in kilobits per second
// can be obtained using CodecAndBitrate's getNumber() function.
enum CodecAndBitrate {
  UNDEFINED = 0;  // Do not pass this to the encoder.
  AMRWB_BITRATE_6KBPS = 6600;
  AMRWB_BITRATE_8KBPS = 8850;
  AMRWB_BITRATE_12KBPS = 12650;
  AMRWB_BITRATE_14KBPS = 14250;
  AMRWB_BITRATE_15KBPS = 15850;
  AMRWB_BITRATE_18KBPS = 18250;
  AMRWB_BITRATE_19KBPS = 19850;
  AMRWB_BITRATE_23KBPS = 23050;
  AMRWB_BITRATE_24KBPS = 23850;
  // For FLAC, the bitrate isn't specified.
  FLAC = 1;
  // Note: Opus isn't actually limited to specific bitrates like AMRWB is.
  //
  // Note that because we run the OggOpusEncoder in low-latency mode, the
  // actual bitrate may be larger than this. See the ogg_opus_encoder lib for
  // details.
  //
  // The effect of this will be larger at low bitrates and low block
  // sizes. This is the bitrate used to configure the codec.
  OGG_OPUS_BITRATE_12KBPS = 12000;
  OGG_OPUS_BITRATE_16KBPS = 16000;
  OGG_OPUS_BITRATE_24KBPS = 24000;
  OGG_OPUS_BITRATE_32KBPS = 32000;
  OGG_OPUS_BITRATE_64KBPS = 64000;
  OGG_OPUS_BITRATE_96KBPS = 96000;
  OGG_OPUS_BITRATE_128KBPS = 128000;
}
