syntax = "proto2";

package audio;

option java_package = "com.citrus.audio";
option java_multiple_files = true;

// Contains information about whether we are connected to the network and
// whether the connection is using WiFi.
message NetworkState {
  // If the network is connected.
  optional bool connected = 1;  // Required.
  // Typically, if false, this means using WiFi.
  optional bool network_metered = 2;  // Required.
}
