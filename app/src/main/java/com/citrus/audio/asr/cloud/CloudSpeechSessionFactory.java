/*
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.citrus.audio.asr.cloud;

import android.support.annotation.GuardedBy;
import com.citrus.audio.asr.CloudSpeechSessionParams;
import com.citrus.audio.asr.SpeechSession;
import com.citrus.audio.asr.SpeechSessionFactory;
import com.citrus.audio.asr.SpeechSessionListener;
import com.google.common.flogger.FluentLogger;

/** A factory for creating cloud sessions. */
public class CloudSpeechSessionFactory implements SpeechSessionFactory {
  private static final FluentLogger logger = FluentLogger.forEnclosingClass();

  /** Lock for handling concurrent accesses to the `params` variable. */
  private final Object paramsLock = new Object();

  @GuardedBy("paramsLock")
  private CloudSpeechSessionParams params;
  private String serviceAccountJsonContent;
  private String projectId;

  public CloudSpeechSessionFactory(CloudSpeechSessionParams params, String serviceAccountJsonContent,
      String projectId) {
    this.params = params;
    this.serviceAccountJsonContent = serviceAccountJsonContent;
    this.projectId = projectId;
  }

  @Override
  public SpeechSession create(SpeechSessionListener listener, int sampleRateHz) {
    synchronized (paramsLock) {
      return new CloudSpeechSession(params, listener, sampleRateHz, serviceAccountJsonContent, projectId);
    }
  }

  @Override
  public void cleanup() {
    // No cleanup needed for v2 API - SpeechClient handles its own lifecycle
    logger.atInfo().log("CloudSpeechSessionFactory cleanup completed");
  }

  public void setParams(CloudSpeechSessionParams params) {
    synchronized (paramsLock) {
      this.params = params;
    }
  }

}
