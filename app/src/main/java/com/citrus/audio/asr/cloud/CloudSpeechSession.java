/*
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// The CloudSpeechSession streams audio to the server until the endpointer tells it to stop. It
// therefore must be repeatedly reopened for continuous transcription. The response observer gets
// data back from the server. Our CloudSpeechStreamObserver extracts the speech and the confidence
// and passes the data to a SpeechSessionListener, which helps to aggregate
// TranscriptionResults and manage the repeatedly reopening sessions.

package com.citrus.audio.asr.cloud;

import com.citrus.audio.StreamingAudioEncoder;
import com.citrus.audio.asr.CloudSpeechSessionParams;
import com.citrus.audio.asr.SpeechRecognitionModelOptions;
import com.citrus.audio.asr.SpeechSession;
import com.citrus.audio.asr.SpeechSessionListener;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.api.gax.rpc.ClientStream;
import com.google.api.gax.rpc.ResponseObserver;
import com.google.auth.Credentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import com.google.cloud.speech.v2.ExplicitDecodingConfig;
import com.google.cloud.speech.v2.RecognitionConfig;
import com.google.cloud.speech.v2.SpeechClient;
import com.google.cloud.speech.v2.SpeechSettings;
import com.google.cloud.speech.v2.StreamingRecognitionConfig;
import com.google.cloud.speech.v2.StreamingRecognizeRequest;
import com.google.cloud.speech.v2.StreamingRecognizeResponse;
import com.google.common.flogger.FluentLogger;
import com.google.protobuf.ByteString;
import java.io.IOException;
import java.io.InputStream;
import org.joda.time.Duration;

/**
 * Lightweight wrapper around the GRPC Google Cloud Speech API. It can handle
 * one streaming
 * recognition request at a time.
 */
public class CloudSpeechSession extends SpeechSession {
  private static final FluentLogger logger = FluentLogger.forEnclosingClass();
  private SpeechClient speechClient;

  // Since the speech session times out after 5 minutes, we should try to avoid
  // sessions reaching
  // approximately that length. If speech begins 4 mins and 30 seconds into the
  // transcription, it
  // will likely be cut off. Therefore, we close sessions that haven't received
  // any results in an
  // extended period of time.
  public static final Duration CLOSE_SESSION_AFTER_EXTENDED_SILENCE_DURATION = Duration.standardMinutes(4)
      .plus(Duration.standardSeconds(30));

  /**
   * The stream observer and cloud interaction functions are marked protected so
   * that they may be
   * replaced with a local server simulation in testing.
   */
  private ResponseObserver<StreamingRecognizeResponse> responseObserver;

  private ClientStream<StreamingRecognizeRequest> requestObserver;

  private final CloudSpeechSessionParams params;
  private final SpeechSessionListener speechSessionListener;
  private int sampleRateHz;
  private final String serviceAccountJsonContent;
  private final String projectId;

  private boolean stillSendingRequests = false;

  private StreamingAudioEncoder encoder;
  private boolean encoderIsRequested;
  private boolean encoderIsSupported;

  /*
   * @param speechSessionListener Listener for recognition responses.
   *
   * @param sampleRateHz Sample rate of microphone data.
   *
   * @param serviceAccountJsonContent The service account JSON content as a
   * string.
   *
   * @param projectId The Google Cloud project ID for the Speech API.
   */
  public CloudSpeechSession(
      CloudSpeechSessionParams params,
      SpeechSessionListener speechSessionListener,
      int sampleRateHz,
      String serviceAccountJsonContent,
      String projectId) {
    this.params = params;
    this.serviceAccountJsonContent = serviceAccountJsonContent;
    this.projectId = projectId;
    this.speechSessionListener = speechSessionListener;
    this.sampleRateHz = sampleRateHz;
    this.encoder = new StreamingAudioEncoder();
  }

  /** Starts a streaming speech recognition request. */
  @Override
  public synchronized void initImpl(
      SpeechRecognitionModelOptions modelOptions, int chunkSizeSamples) {
    if (chunkSizeSamples < 0.050 * sampleRateHz) {
      logger.atWarning().log(
          "Your buffer size is less than 50ms, you may have poor performance getting "
              + "streaming results.");
    }
    responseObserver = makeResponseObserver(speechSessionListener);
    encoderIsRequested = params.getEncoderParams().getEnableEncoder();
    encoderIsSupported = StreamingAudioEncoder.isEncoderSupported(params.getEncoderParams().getCodec());
    if (usingEncoder()) {
      try {
        encoder.init(
            sampleRateHz,
            params.getEncoderParams().getCodec(),
            params.getEncoderParams().getAllowVbr());
      } catch (StreamingAudioEncoder.EncoderException | IOException e) {
        e.printStackTrace();
        logger.atSevere().log("Encoder could not be created. Using uncompressed audio.");
        encoderIsRequested = false;
      }
    }
    initServer(modelOptions);

    stillSendingRequests = true;
  }

  /** Returns true when the encoder is being used. */
  public boolean usingEncoder() {
    return encoderIsRequested && encoderIsSupported;
  }

  private ResponseObserver<StreamingRecognizeResponse> makeResponseObserver(
      SpeechSessionListener speechSessionListener) {
    return new CloudSpeechStreamObserver(
        params.getObserverParams(), speechSessionListener, sessionID());
  }

  /**
   * Sends an audio buffer to the Cloud Speech Server.
   *
   * @param buffer 16 bit LinearPCM byte array.
   * @param offset first element of buffer to use.
   * @param count  number of elements of buffer to use.
   * @return true if audio data was processed, false if session was already
   *         requested to close. You
   *         should wait for the recognition listener passed into the constructor
   *         to receive
   *         OK_TO_TERMINATE before destroying the session.
   */
  @Override
  public synchronized boolean processAudioBytesImpl(byte[] buffer, int offset, int count) {
    if (!isStillSendingRequests()) {
      return false;
    }

    if (usingEncoder()) {
      byte[] encoded = encoder.processAudioBytes(buffer, offset, count);
      if (encoded.length > 0) {
        streamToServer(encoded, 0, encoded.length);
      }
    } else {
      streamToServer(buffer, offset, count);
    }

    if (CLOSE_SESSION_AFTER_EXTENDED_SILENCE_DURATION.isShorterThan(
        ((CloudSpeechStreamObserver) responseObserver).timeSinceLastServerActivity())) {
      logger.atInfo().log(
          "Session #%d scheduled to be ended due to extended silence.", sessionID());
      requestCloseSession();
    }
    return true;
  }

  private boolean isStillSendingRequests() {
    return stillSendingRequests && ((CloudSpeechStreamObserver) responseObserver).isStillListening();
  }

  /**
   * Closes the current recognition request on the client end. This does not
   * immediately end the
   * session. Only once the server acknowledges the closing of the session is
   * communication
   * complete.
   */
  @Override
  public synchronized void requestCloseSessionImpl() {
    if (stillSendingRequests) {
      stillSendingRequests = false;
      if (usingEncoder()) {
        // Get any remaining output from the codec and stop.
        byte[] data = encoder.flushAndStop();
        streamToServer(data, 0, data.length);
      }
      closeServer();
    }
  }

  @Override
  public boolean requiresNetworkConnection() {
    return true;
  }

  private void initServer(SpeechRecognitionModelOptions modelOptions) {
    try {
      // Create SpeechClient with service account authentication for v2 API
      // Load service account credentials from JSON content
      if (serviceAccountJsonContent == null || serviceAccountJsonContent.trim().isEmpty()) {
        throw new RuntimeException("Service account JSON content is null or empty");
      }

      // Normalize the JSON content to handle escaped newlines
      String normalizedJsonContent = serviceAccountJsonContent.replace("\\n", "\n");

      ByteArrayInputStream credentialsStream = new ByteArrayInputStream(
          normalizedJsonContent.getBytes(StandardCharsets.UTF_8));
      Credentials credentials = ServiceAccountCredentials.fromStream(credentialsStream);

      SpeechSettings speechSettings = SpeechSettings.newBuilder()
          .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
          .build();

      this.speechClient = SpeechClient.create(speechSettings);
      requestObserver = speechClient.streamingRecognizeCallable().splitCall(responseObserver);

      logger.atInfo().log("Successfully initialized SpeechClient for project: %s", projectId);
    } catch (Exception e) {
      logger.atSevere().withCause(e).log("Failed to create SpeechClient with service account authentication");
      throw new RuntimeException("Failed to initialize speech client: " + e.getMessage(), e);
    }

    // Build and send a StreamingRecognizeRequest containing the parameters for
    // processing the audio.

    // Configure audio encoding using ExplicitDecodingConfig for v2 API
    ExplicitDecodingConfig.AudioEncoding encodingType = ExplicitDecodingConfig.AudioEncoding.LINEAR16;
    if (usingEncoder()) {
      switch (encoder.getCodecType()) {
        case AMRWB:
          encodingType = ExplicitDecodingConfig.AudioEncoding.AMR_WB;
          break;
        case FLAC:
          encodingType = ExplicitDecodingConfig.AudioEncoding.FLAC;
          break;
        case OGG_OPUS:
          encodingType = ExplicitDecodingConfig.AudioEncoding.OGG_OPUS;
          break;
        default:
      }
    }

    // Create ExplicitDecodingConfig for v2 API
    ExplicitDecodingConfig decodingConfig = ExplicitDecodingConfig.newBuilder()
        .setEncoding(encodingType)
        .setSampleRateHertz(sampleRateHz)
        .setAudioChannelCount(1)
        .build();

    // Build RecognitionConfig for v2 API
    RecognitionConfig.Builder configBuilder = RecognitionConfig.newBuilder()
        .setExplicitDecodingConfig(decodingConfig)
        .addLanguageCodes(modelOptions.getLocale());

    // Add speech contexts (bias words) if available
    // Note: In v2 API, speech contexts may be configured differently
    // For now, we'll skip this feature and add it back once we verify the correct
    // v2 structure
    // if (!modelOptions.getBiasWordsList().isEmpty()) {
    // configBuilder.addSpeechContexts(
    // RecognitionConfig.SpeechContext.newBuilder()
    // .addAllPhrases(modelOptions.getBiasWordsList())
    // .build());
    // }

    // Set model based on options
    switch (modelOptions.getModel()) {
      case VIDEO:
        if (!modelOptions.getLocale().equals("en-US")) {
          logger.atSevere().log("Only en-US is supported by YouTube Livestream model");
        }
        configBuilder.setModel("video");
        break;
      case DICTATION_DEFAULT:
        configBuilder.setModel("latest_long");
        break;
    }

    RecognitionConfig config = configBuilder.build();

    // Build StreamingRecognitionConfig for v2 API
    StreamingRecognitionConfig streamingConfig = StreamingRecognitionConfig.newBuilder()
        .setConfig(config)
        .build();

    // Create recognizer resource name using the provided project ID
    // For v2 API, we use the default recognizer with the specified project
    String recognizerName = String.format("projects/%s/locations/global/recognizers/_", projectId);

    // First request sends the configuration and recognizer.
    StreamingRecognizeRequest initial = StreamingRecognizeRequest.newBuilder()
        .setRecognizer(recognizerName)
        .setStreamingConfig(streamingConfig)
        .build();

    requestObserver.send(initial);
  }

  private void streamToServer(byte[] buffer, int offset, int count) {
    StreamingRecognizeRequest request = StreamingRecognizeRequest.newBuilder()
        .setAudio(ByteString.copyFrom(buffer, offset, count))
        .build();
    requestObserver.send(request);
  }

  private void closeServer() {
    if (requestObserver != null) {
      // Tell the server we're done sending.
      requestObserver.closeSend();
      requestObserver = null;
    }
    if (speechClient != null) {
      speechClient.close();
      speechClient = null;
    }
  }
}
