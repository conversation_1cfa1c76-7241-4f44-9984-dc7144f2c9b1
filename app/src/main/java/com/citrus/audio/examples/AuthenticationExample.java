package com.citrus.audio.examples;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import com.citrus.audio.R;
import com.citrus.audio.auth.GoogleCloudAuthManager;
import com.citrus.audio.auth.CredentialValidator;
import com.citrus.audio.auth.SecureCredentialStorage;
import com.citrus.audio.auth.AuthenticationErrorHandler;
import com.google.cloud.speech.v2.SpeechClient;

/**
 * Example activity demonstrating Google Cloud authentication implementation.
 * This shows the complete flow from user input to successful authentication.
 */
public class AuthenticationExample extends Activity {
    private static final String TAG = "AuthExample";
    
    // UI components
    private EditText projectIdInput;
    private EditText serviceAccountJsonInput;
    private Button authenticateButton;
    private Button clearButton;
    private Button loadStoredButton;
    private ProgressBar progressBar;
    private TextView statusText;
    private TextView diagnosticText;
    
    // Authentication components
    private GoogleCloudAuthManager authManager;
    private SecureCredentialStorage credentialStorage;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_authentication_example);
        
        initializeComponents();
        setupEventHandlers();
        loadStoredCredentials();
    }
    
    private void initializeComponents() {
        // Initialize UI components
        projectIdInput = findViewById(R.id.project_id_input);
        serviceAccountJsonInput = findViewById(R.id.service_account_json_input);
        authenticateButton = findViewById(R.id.authenticate_button);
        clearButton = findViewById(R.id.clear_button);
        loadStoredButton = findViewById(R.id.load_stored_button);
        progressBar = findViewById(R.id.progress_bar);
        statusText = findViewById(R.id.status_text);
        diagnosticText = findViewById(R.id.diagnostic_text);
        
        // Initialize authentication components
        authManager = new GoogleCloudAuthManager();
        credentialStorage = new SecureCredentialStorage(this);
        
        // Set initial UI state
        progressBar.setVisibility(View.GONE);
        updateStatus("Ready to authenticate", false);
    }
    
    private void setupEventHandlers() {
        authenticateButton.setOnClickListener(v -> performAuthentication());
        clearButton.setOnClickListener(v -> clearCredentials());
        loadStoredButton.setOnClickListener(v -> loadStoredCredentials());
    }
    
    private void performAuthentication() {
        String projectId = projectIdInput.getText().toString().trim();
        String serviceAccountJson = serviceAccountJsonInput.getText().toString().trim();
        
        // Basic input validation
        if (projectId.isEmpty() || serviceAccountJson.isEmpty()) {
            Toast.makeText(this, "Please enter both Project ID and Service Account JSON", 
                Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Show progress
        setUIEnabled(false);
        progressBar.setVisibility(View.VISIBLE);
        updateStatus("Validating credentials...", false);
        
        // Validate credentials format
        if (!validateCredentials(projectId, serviceAccountJson)) {
            setUIEnabled(true);
            progressBar.setVisibility(View.GONE);
            return;
        }
        
        // Perform authentication
        updateStatus("Authenticating with Google Cloud...", false);
        
        authManager.setCredentials(projectId, serviceAccountJson, 
            new GoogleCloudAuthManager.AuthenticationCallback() {
                @Override
                public void onSuccess(String message) {
                    runOnUiThread(() -> {
                        setUIEnabled(true);
                        progressBar.setVisibility(View.GONE);
                        updateStatus("Authentication successful!", true);
                        
                        // Store credentials securely
                        boolean stored = credentialStorage.storeCredentials(projectId, serviceAccountJson);
                        if (stored) {
                            Toast.makeText(AuthenticationExample.this, 
                                "Credentials authenticated and stored securely", 
                                Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(AuthenticationExample.this, 
                                "Authentication successful but storage failed", 
                                Toast.LENGTH_SHORT).show();
                        }
                        
                        updateDiagnosticInfo(projectId, serviceAccountJson);
                    });
                }
                
                @Override
                public void onError(GoogleCloudAuthManager.AuthenticationError error) {
                    runOnUiThread(() -> {
                        setUIEnabled(true);
                        progressBar.setVisibility(View.GONE);
                        updateStatus("Authentication failed: " + error.getMessage(), false);
                        
                        Toast.makeText(AuthenticationExample.this, 
                            "Authentication failed: " + error.getMessage(), 
                            Toast.LENGTH_LONG).show();
                        
                        updateDiagnosticInfo(projectId, serviceAccountJson);
                    });
                }
            });
    }
    
    private boolean validateCredentials(String projectId, String serviceAccountJson) {
        // Validate project ID
        CredentialValidator.ValidationResult projectValidation = 
            CredentialValidator.validateProjectId(projectId);
        if (!projectValidation.isValid()) {
            updateStatus("Invalid Project ID: " + projectValidation.getErrorMessage(), false);
            Toast.makeText(this, "Invalid Project ID: " + projectValidation.getErrorMessage(), 
                Toast.LENGTH_LONG).show();
            return false;
        }
        
        // Validate service account JSON
        CredentialValidator.ValidationResult jsonValidation = 
            CredentialValidator.validateServiceAccountJson(serviceAccountJson);
        if (!jsonValidation.isValid()) {
            updateStatus("Invalid Service Account JSON: " + jsonValidation.getErrorMessage(), false);
            Toast.makeText(this, "Invalid Service Account JSON: " + jsonValidation.getErrorMessage(), 
                Toast.LENGTH_LONG).show();
            return false;
        }
        
        // Validate project ID match
        CredentialValidator.ValidationResult matchValidation = 
            CredentialValidator.validateProjectIdMatch(projectId, serviceAccountJson);
        if (!matchValidation.isValid()) {
            updateStatus("Project ID mismatch: " + matchValidation.getErrorMessage(), false);
            Toast.makeText(this, matchValidation.getErrorMessage(), Toast.LENGTH_LONG).show();
            return false;
        }
        
        return true;
    }
    
    private void clearCredentials() {
        // Clear UI
        projectIdInput.setText("");
        serviceAccountJsonInput.setText("");
        
        // Clear stored credentials
        boolean cleared = credentialStorage.clearCredentials();
        
        // Clear authentication manager
        authManager.clearCredentials();
        
        updateStatus("Credentials cleared", false);
        diagnosticText.setText("");
        
        Toast.makeText(this, cleared ? "Credentials cleared successfully" : "Failed to clear credentials", 
            Toast.LENGTH_SHORT).show();
    }
    
    private void loadStoredCredentials() {
        if (credentialStorage.hasStoredCredentials()) {
            if (credentialStorage.validateStoredCredentials()) {
                String projectId = credentialStorage.getProjectId();
                String serviceAccountJson = credentialStorage.getServiceAccountJson();
                
                projectIdInput.setText(projectId);
                serviceAccountJsonInput.setText(serviceAccountJson);
                
                updateStatus("Stored credentials loaded", false);
                Toast.makeText(this, "Stored credentials loaded successfully", Toast.LENGTH_SHORT).show();
                
                updateDiagnosticInfo(projectId, serviceAccountJson);
            } else {
                updateStatus("Stored credentials are invalid", false);
                Toast.makeText(this, "Stored credentials are invalid and will be cleared", 
                    Toast.LENGTH_LONG).show();
                credentialStorage.clearCredentials();
            }
        } else {
            updateStatus("No stored credentials found", false);
            Toast.makeText(this, "No stored credentials found", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void updateStatus(String message, boolean isSuccess) {
        statusText.setText(message);
        statusText.setTextColor(getResources().getColor(
            isSuccess ? android.R.color.holo_green_dark : android.R.color.holo_red_dark));
        Log.i(TAG, "Status: " + message);
    }
    
    private void setUIEnabled(boolean enabled) {
        authenticateButton.setEnabled(enabled);
        clearButton.setEnabled(enabled);
        loadStoredButton.setEnabled(enabled);
        projectIdInput.setEnabled(enabled);
        serviceAccountJsonInput.setEnabled(enabled);
    }
    
    private void updateDiagnosticInfo(String projectId, String serviceAccountJson) {
        try {
            String diagnostic = AuthenticationErrorHandler.getDiagnosticInfo(
                this, projectId, serviceAccountJson);
            diagnosticText.setText(diagnostic);
        } catch (Exception e) {
            Log.e(TAG, "Error generating diagnostic info", e);
            diagnosticText.setText("Error generating diagnostic information");
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (authManager != null) {
            authManager.cleanup();
        }
    }
    
    /**
     * Example of how to use the authenticated credentials
     */
    private void useAuthenticatedCredentials() {
        if (authManager.isAuthenticated()) {
            try {
                // Get the authenticated Speech client
                SpeechClient speechClient = authManager.getSpeechClient();
                
                // Use the client for speech recognition
                // This is where you would integrate with your existing speech recognition code
                
                Log.i(TAG, "Speech client ready for use");
                
            } catch (Exception e) {
                Log.e(TAG, "Error getting speech client", e);
                Toast.makeText(this, "Error accessing speech client: " + e.getMessage(), 
                    Toast.LENGTH_LONG).show();
            }
        } else {
            Toast.makeText(this, "Not authenticated. Please authenticate first.", 
                Toast.LENGTH_SHORT).show();
        }
    }
}
