package com.citrus.audio;

import android.util.Log;
import android.content.Context;

public class JavaInputSDK {
    static {
        // This block runs when the class is loaded
        Log.d("", "JavaInputSDK class loaded getVersionStatic");
    }

    public static final String VERSION_1 = "1.0.0";
    public static final int PERMISSIONS_REQUEST_RECORD_AUDIO = 1;

    public static String getVersionStatic() {
        Log.d("", "JavaInputSDK class loaded getVersionStatic");
        return "VERSION_1";
    }
}