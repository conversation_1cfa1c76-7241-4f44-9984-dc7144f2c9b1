package com.citrus.audio.auth;

import android.content.Context;
import android.util.Log;
import com.google.auth.Credentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.speech.v2.SpeechClient;
import com.google.cloud.speech.v2.SpeechSettings;
import com.google.api.gax.core.FixedCredentialsProvider;
import org.json.JSONObject;
import org.json.JSONException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Manages Google Cloud authentication using user-provided service account
 * credentials.
 * Handles credential validation, authentication testing, and secure credential
 * storage.
 */
public class GoogleCloudAuthManager {
    private static final String TAG = "GoogleCloudAuthManager";

    // Thread pool for async operations
    private static final ExecutorService executor = Executors.newCachedThreadPool();

    private String projectId;
    private String serviceAccountJson;
    private Credentials credentials;
    private SpeechClient speechClient;
    private boolean isAuthenticated = false;

    /**
     * Authentication result callback interface
     */
    public interface AuthenticationCallback {
        void onSuccess(String message);

        void onError(AuthenticationError error);
    }

    /**
     * Authentication error types with user-friendly messages
     */
    public enum AuthenticationError {
        INVALID_JSON("Invalid JSON format. Please check your service account JSON."),
        MISSING_REQUIRED_FIELDS("Service account JSON is missing required fields."),
        INVALID_CREDENTIALS("Invalid credentials. Please verify your service account key."),
        NETWORK_ERROR("Network error. Please check your internet connection."),
        PROJECT_ID_MISMATCH("Project ID doesn't match the service account."),
        PERMISSION_DENIED("Service account doesn't have required permissions."),
        UNKNOWN_ERROR("An unexpected error occurred during authentication.");

        private final String message;

        AuthenticationError(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * Validates and sets the authentication credentials
     */
    public void setCredentials(String projectId, String serviceAccountJson, AuthenticationCallback callback) {
        this.projectId = projectId;
        this.serviceAccountJson = serviceAccountJson;

        // Perform validation and authentication asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                // Validate inputs
                if (projectId == null || projectId.trim().isEmpty()) {
                    callback.onError(AuthenticationError.MISSING_REQUIRED_FIELDS);
                    return;
                }

                if (serviceAccountJson == null || serviceAccountJson.trim().isEmpty()) {
                    callback.onError(AuthenticationError.MISSING_REQUIRED_FIELDS);
                    return;
                }

                // Validate JSON format and required fields
                AuthenticationError validationError = validateServiceAccountJson(serviceAccountJson);
                if (validationError != null) {
                    callback.onError(validationError);
                    return;
                }

                // Create credentials from JSON
                // Normalize the JSON content to handle escaped newlines
                String normalizedServiceAccountJson = serviceAccountJson.replace("\\n", "\n");

                ByteArrayInputStream credentialsStream = new ByteArrayInputStream(
                        normalizedServiceAccountJson.getBytes(StandardCharsets.UTF_8));
                credentials = ServiceAccountCredentials.fromStream(credentialsStream);

                // Verify project ID matches
                if (!verifyProjectIdMatch(serviceAccountJson, projectId)) {
                    callback.onError(AuthenticationError.PROJECT_ID_MISMATCH);
                    return;
                }

                // Test authentication by creating a speech client
                testAuthentication(callback);

            } catch (IOException e) {
                Log.e(TAG, "Error creating credentials", e);
                callback.onError(AuthenticationError.INVALID_CREDENTIALS);
            } catch (Exception e) {
                Log.e(TAG, "Unexpected error during authentication", e);
                callback.onError(AuthenticationError.UNKNOWN_ERROR);
            }
        }, executor);
    }

    /**
     * Validates the service account JSON format and required fields
     */
    private AuthenticationError validateServiceAccountJson(String jsonString) {
        try {
            JSONObject json = new JSONObject(jsonString);

            // Check required fields
            String[] requiredFields = {
                    "type", "project_id", "private_key_id", "private_key",
                    "client_email", "client_id", "auth_uri", "token_uri"
            };

            for (String field : requiredFields) {
                if (!json.has(field) || json.getString(field).trim().isEmpty()) {
                    Log.e(TAG, "Missing required field: " + field);
                    return AuthenticationError.MISSING_REQUIRED_FIELDS;
                }
            }

            // Verify it's a service account
            if (!"service_account".equals(json.getString("type"))) {
                Log.e(TAG, "JSON is not a service account type");
                return AuthenticationError.INVALID_CREDENTIALS;
            }

            return null; // Validation passed

        } catch (JSONException e) {
            Log.e(TAG, "Invalid JSON format", e);
            return AuthenticationError.INVALID_JSON;
        }
    }

    /**
     * Verifies that the project ID in the JSON matches the provided project ID
     */
    private boolean verifyProjectIdMatch(String jsonString, String expectedProjectId) {
        try {
            JSONObject json = new JSONObject(jsonString);
            String jsonProjectId = json.getString("project_id");
            return expectedProjectId.equals(jsonProjectId);
        } catch (JSONException e) {
            Log.e(TAG, "Error verifying project ID", e);
            return false;
        }
    }

    /**
     * Tests authentication by attempting to create a Speech client
     */
    private void testAuthentication(AuthenticationCallback callback) {
        try {
            // Create Speech client with the credentials
            SpeechSettings speechSettings = SpeechSettings.newBuilder()
                    .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                    .build();

            if (speechClient != null) {
                speechClient.close();
            }

            speechClient = SpeechClient.create(speechSettings);
            isAuthenticated = true;

            Log.i(TAG, "Authentication successful for project: " + projectId);
            callback.onSuccess("Authentication successful!");

        } catch (Exception e) {
            Log.e(TAG, "Authentication test failed", e);
            isAuthenticated = false;

            // Use the error handler to analyze the exception
            AuthenticationErrorHandler.ErrorInfo errorInfo = AuthenticationErrorHandler.analyzeError(e);

            // Map to our authentication error types
            AuthenticationError authError;
            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                String lowerMessage = errorMessage.toLowerCase();
                if (lowerMessage.contains("permission_denied")) {
                    authError = AuthenticationError.PERMISSION_DENIED;
                } else if (lowerMessage.contains("unauthenticated")) {
                    authError = AuthenticationError.INVALID_CREDENTIALS;
                } else if (lowerMessage.contains("network") || lowerMessage.contains("timeout")) {
                    authError = AuthenticationError.NETWORK_ERROR;
                } else {
                    authError = AuthenticationError.UNKNOWN_ERROR;
                }
            } else {
                authError = AuthenticationError.UNKNOWN_ERROR;
            }

            callback.onError(authError);
        }
    }

    /**
     * Gets the authenticated credentials
     */
    public Credentials getCredentials() {
        return credentials;
    }

    /**
     * Gets the Speech client (creates one if not exists)
     */
    public SpeechClient getSpeechClient() throws IOException {
        if (!isAuthenticated || credentials == null) {
            throw new IllegalStateException("Not authenticated. Call setCredentials first.");
        }

        if (speechClient == null) {
            SpeechSettings speechSettings = SpeechSettings.newBuilder()
                    .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                    .build();
            speechClient = SpeechClient.create(speechSettings);
        }

        return speechClient;
    }

    /**
     * Gets the project ID
     */
    public String getProjectId() {
        return projectId;
    }

    /**
     * Checks if currently authenticated
     */
    public boolean isAuthenticated() {
        return isAuthenticated;
    }

    /**
     * Clears stored credentials and closes clients
     */
    public void clearCredentials() {
        projectId = null;
        serviceAccountJson = null;
        credentials = null;
        isAuthenticated = false;

        if (speechClient != null) {
            speechClient.close();
            speechClient = null;
        }

        Log.i(TAG, "Credentials cleared");
    }

    /**
     * Cleanup method to be called when done
     */
    public void cleanup() {
        clearCredentials();
        executor.shutdown();
    }
}
