package com.citrus.audio.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.security.keystore.KeyGenParameterSpec;
import android.security.keystore.KeyProperties;
import android.util.Base64;
import android.util.Log;
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;
import java.security.GeneralSecurityException;
import java.io.IOException;

/**
 * Secure storage for Google Cloud credentials using Android's encrypted shared preferences.
 * This class provides secure storage and retrieval of sensitive authentication data.
 */
public class SecureCredentialStorage {
    private static final String TAG = "SecureCredentialStorage";
    private static final String PREFS_NAME = "google_cloud_credentials";
    private static final String KEY_PROJECT_ID = "project_id";
    private static final String KEY_SERVICE_ACCOUNT_JSON = "service_account_json";
    private static final String KEY_LAST_AUTH_TIME = "last_auth_time";
    
    private final Context context;
    private SharedPreferences encryptedPrefs;
    
    public SecureCredentialStorage(Context context) {
        this.context = context.getApplicationContext();
        initializeEncryptedPreferences();
    }
    
    /**
     * Initialize encrypted shared preferences with Android Keystore
     */
    private void initializeEncryptedPreferences() {
        try {
            // Create or retrieve the master key for encryption
            MasterKey masterKey = new MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .setRequestStrongBoxBacked(true) // Use hardware security module if available
                .setUserAuthenticationRequired(false) // Don't require user auth for each access
                .build();
            
            // Create encrypted shared preferences
            encryptedPrefs = EncryptedSharedPreferences.create(
                context,
                PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
            
            Log.i(TAG, "Encrypted shared preferences initialized successfully");
            
        } catch (GeneralSecurityException | IOException e) {
            Log.e(TAG, "Failed to initialize encrypted shared preferences", e);
            // Fallback to regular shared preferences (not recommended for production)
            encryptedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            Log.w(TAG, "Using fallback unencrypted storage - credentials are not secure!");
        }
    }
    
    /**
     * Stores Google Cloud credentials securely
     */
    public boolean storeCredentials(String projectId, String serviceAccountJson) {
        if (projectId == null || serviceAccountJson == null) {
            Log.e(TAG, "Cannot store null credentials");
            return false;
        }
        
        try {
            SharedPreferences.Editor editor = encryptedPrefs.edit();
            
            // Store the credentials
            editor.putString(KEY_PROJECT_ID, projectId);
            editor.putString(KEY_SERVICE_ACCOUNT_JSON, serviceAccountJson);
            editor.putLong(KEY_LAST_AUTH_TIME, System.currentTimeMillis());
            
            boolean success = editor.commit();
            
            if (success) {
                Log.i(TAG, "Credentials stored securely");
            } else {
                Log.e(TAG, "Failed to store credentials");
            }
            
            return success;
            
        } catch (Exception e) {
            Log.e(TAG, "Error storing credentials", e);
            return false;
        }
    }
    
    /**
     * Retrieves stored project ID
     */
    public String getProjectId() {
        try {
            return encryptedPrefs.getString(KEY_PROJECT_ID, null);
        } catch (Exception e) {
            Log.e(TAG, "Error retrieving project ID", e);
            return null;
        }
    }
    
    /**
     * Retrieves stored service account JSON
     */
    public String getServiceAccountJson() {
        try {
            return encryptedPrefs.getString(KEY_SERVICE_ACCOUNT_JSON, null);
        } catch (Exception e) {
            Log.e(TAG, "Error retrieving service account JSON", e);
            return null;
        }
    }
    
    /**
     * Gets the timestamp of the last successful authentication
     */
    public long getLastAuthTime() {
        try {
            return encryptedPrefs.getLong(KEY_LAST_AUTH_TIME, 0);
        } catch (Exception e) {
            Log.e(TAG, "Error retrieving last auth time", e);
            return 0;
        }
    }
    
    /**
     * Checks if credentials are stored
     */
    public boolean hasStoredCredentials() {
        String projectId = getProjectId();
        String serviceAccountJson = getServiceAccountJson();
        return projectId != null && !projectId.isEmpty() && 
               serviceAccountJson != null && !serviceAccountJson.isEmpty();
    }
    
    /**
     * Checks if stored credentials are recent (within 24 hours)
     */
    public boolean areCredentialsRecent() {
        long lastAuthTime = getLastAuthTime();
        if (lastAuthTime == 0) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        long timeDifference = currentTime - lastAuthTime;
        long twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        
        return timeDifference < twentyFourHours;
    }
    
    /**
     * Clears all stored credentials
     */
    public boolean clearCredentials() {
        try {
            SharedPreferences.Editor editor = encryptedPrefs.edit();
            editor.remove(KEY_PROJECT_ID);
            editor.remove(KEY_SERVICE_ACCOUNT_JSON);
            editor.remove(KEY_LAST_AUTH_TIME);
            
            boolean success = editor.commit();
            
            if (success) {
                Log.i(TAG, "Credentials cleared successfully");
            } else {
                Log.e(TAG, "Failed to clear credentials");
            }
            
            return success;
            
        } catch (Exception e) {
            Log.e(TAG, "Error clearing credentials", e);
            return false;
        }
    }
    
    /**
     * Updates the last authentication time
     */
    public boolean updateLastAuthTime() {
        try {
            SharedPreferences.Editor editor = encryptedPrefs.edit();
            editor.putLong(KEY_LAST_AUTH_TIME, System.currentTimeMillis());
            return editor.commit();
        } catch (Exception e) {
            Log.e(TAG, "Error updating last auth time", e);
            return false;
        }
    }
    
    /**
     * Validates that stored credentials are not corrupted
     */
    public boolean validateStoredCredentials() {
        String projectId = getProjectId();
        String serviceAccountJson = getServiceAccountJson();
        
        if (projectId == null || serviceAccountJson == null) {
            return false;
        }
        
        // Basic validation
        CredentialValidator.ValidationResult projectValidation = 
            CredentialValidator.validateProjectId(projectId);
        if (!projectValidation.isValid()) {
            Log.w(TAG, "Stored project ID is invalid: " + projectValidation.getErrorMessage());
            return false;
        }
        
        CredentialValidator.ValidationResult jsonValidation = 
            CredentialValidator.validateServiceAccountJson(serviceAccountJson);
        if (!jsonValidation.isValid()) {
            Log.w(TAG, "Stored service account JSON is invalid: " + jsonValidation.getErrorMessage());
            return false;
        }
        
        CredentialValidator.ValidationResult matchValidation = 
            CredentialValidator.validateProjectIdMatch(projectId, serviceAccountJson);
        if (!matchValidation.isValid()) {
            Log.w(TAG, "Stored credentials have mismatched project IDs");
            return false;
        }
        
        return true;
    }
    
    /**
     * Gets a summary of stored credential status (for debugging)
     */
    public String getCredentialStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Credentials stored: ").append(hasStoredCredentials()).append("\n");
        status.append("Credentials recent: ").append(areCredentialsRecent()).append("\n");
        status.append("Credentials valid: ").append(validateStoredCredentials()).append("\n");
        
        long lastAuth = getLastAuthTime();
        if (lastAuth > 0) {
            status.append("Last auth: ").append(new java.util.Date(lastAuth)).append("\n");
        }
        
        String projectId = getProjectId();
        if (projectId != null) {
            status.append("Project ID: ").append(projectId).append("\n");
        }
        
        return status.toString();
    }
}
