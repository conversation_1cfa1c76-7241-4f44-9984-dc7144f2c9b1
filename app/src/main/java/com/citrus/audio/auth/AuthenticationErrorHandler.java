package com.citrus.audio.auth;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;
import java.util.HashMap;
import java.util.Map;

/**
 * Comprehensive error handling for Google Cloud authentication failures.
 * Provides user-friendly error messages and suggested solutions.
 */
public class AuthenticationErrorHandler {
    private static final String TAG = "AuthErrorHandler";
    
    /**
     * Detailed error information with user-friendly messages and solutions
     */
    public static class ErrorInfo {
        private final String userMessage;
        private final String technicalMessage;
        private final String suggestedSolution;
        private final boolean isRetryable;
        
        public ErrorInfo(String userMessage, String technicalMessage, String suggestedSolution, boolean isRetryable) {
            this.userMessage = userMessage;
            this.technicalMessage = technicalMessage;
            this.suggestedSolution = suggestedSolution;
            this.isRetryable = isRetryable;
        }
        
        public String getUserMessage() { return userMessage; }
        public String getTechnicalMessage() { return technicalMessage; }
        public String getSuggestedSolution() { return suggestedSolution; }
        public boolean isRetryable() { return isRetryable; }
    }
    
    // Map of common error patterns to user-friendly explanations
    private static final Map<String, ErrorInfo> ERROR_PATTERNS = new HashMap<>();
    
    static {
        // Network-related errors
        ERROR_PATTERNS.put("network", new ErrorInfo(
            "Network connection error",
            "Unable to connect to Google Cloud services",
            "Check your internet connection and try again",
            true
        ));
        
        ERROR_PATTERNS.put("timeout", new ErrorInfo(
            "Connection timeout",
            "Request to Google Cloud services timed out",
            "Check your internet connection and try again",
            true
        ));
        
        ERROR_PATTERNS.put("dns", new ErrorInfo(
            "DNS resolution failed",
            "Unable to resolve Google Cloud service addresses",
            "Check your internet connection and DNS settings",
            true
        ));
        
        // Authentication errors
        ERROR_PATTERNS.put("unauthenticated", new ErrorInfo(
            "Invalid credentials",
            "The provided service account credentials are invalid",
            "Verify your service account JSON file is correct and not expired",
            false
        ));
        
        ERROR_PATTERNS.put("permission_denied", new ErrorInfo(
            "Permission denied",
            "Service account lacks required permissions",
            "Ensure your service account has Speech API permissions enabled",
            false
        ));
        
        ERROR_PATTERNS.put("invalid_argument", new ErrorInfo(
            "Invalid request",
            "The request parameters are invalid",
            "Check your project ID and service account configuration",
            false
        ));
        
        // Service errors
        ERROR_PATTERNS.put("unavailable", new ErrorInfo(
            "Service unavailable",
            "Google Cloud Speech service is temporarily unavailable",
            "Try again in a few minutes",
            true
        ));
        
        ERROR_PATTERNS.put("quota_exceeded", new ErrorInfo(
            "Quota exceeded",
            "API quota or rate limit exceeded",
            "Wait before making more requests or check your quota limits",
            true
        ));
        
        ERROR_PATTERNS.put("resource_exhausted", new ErrorInfo(
            "Resource exhausted",
            "API rate limit or quota exceeded",
            "Reduce request frequency or upgrade your quota",
            true
        ));
        
        // Configuration errors
        ERROR_PATTERNS.put("not_found", new ErrorInfo(
            "Resource not found",
            "Project or resource not found",
            "Verify your project ID is correct and the project exists",
            false
        ));
        
        ERROR_PATTERNS.put("failed_precondition", new ErrorInfo(
            "Service not enabled",
            "Speech API may not be enabled for this project",
            "Enable the Cloud Speech-to-Text API in your Google Cloud Console",
            false
        ));
    }
    
    /**
     * Analyzes an exception and returns detailed error information
     */
    public static ErrorInfo analyzeError(Exception exception) {
        if (exception == null) {
            return new ErrorInfo(
                "Unknown error occurred",
                "No exception details available",
                "Try again or contact support",
                true
            );
        }
        
        String errorMessage = exception.getMessage();
        if (errorMessage == null) {
            errorMessage = exception.getClass().getSimpleName();
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        
        // Check for specific error patterns
        for (Map.Entry<String, ErrorInfo> entry : ERROR_PATTERNS.entrySet()) {
            if (lowerMessage.contains(entry.getKey())) {
                Log.d(TAG, "Matched error pattern: " + entry.getKey());
                return entry.getValue();
            }
        }
        
        // Check for specific exception types
        if (exception instanceof java.net.UnknownHostException) {
            return ERROR_PATTERNS.get("network");
        } else if (exception instanceof java.net.SocketTimeoutException) {
            return ERROR_PATTERNS.get("timeout");
        } else if (exception instanceof java.io.IOException) {
            return new ErrorInfo(
                "Connection error",
                "I/O error occurred during authentication",
                "Check your internet connection and try again",
                true
            );
        } else if (exception instanceof SecurityException) {
            return new ErrorInfo(
                "Security error",
                "Security exception during authentication",
                "Check your service account permissions",
                false
            );
        }
        
        // Default error info for unrecognized errors
        return new ErrorInfo(
            "Authentication failed",
            errorMessage,
            "Verify your credentials and try again",
            true
        );
    }
    
    /**
     * Handles authentication errors with appropriate user feedback
     */
    public static void handleAuthenticationError(Context context, Exception exception, 
                                               AuthenticationCallback callback) {
        ErrorInfo errorInfo = analyzeError(exception);
        
        Log.e(TAG, "Authentication error: " + errorInfo.getTechnicalMessage(), exception);
        
        // Show user-friendly message
        String fullMessage = errorInfo.getUserMessage() + "\n\n" + errorInfo.getSuggestedSolution();
        
        if (context != null) {
            Toast.makeText(context, fullMessage, Toast.LENGTH_LONG).show();
        }
        
        // Call callback with error details
        if (callback != null) {
            callback.onAuthenticationError(errorInfo);
        }
    }
    
    /**
     * Callback interface for error handling
     */
    public interface AuthenticationCallback {
        void onAuthenticationError(ErrorInfo errorInfo);
    }
    
    /**
     * Validates network connectivity before authentication
     */
    public static boolean isNetworkAvailable(Context context) {
        try {
            android.net.ConnectivityManager connectivityManager = 
                (android.net.ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            
            if (connectivityManager != null) {
                android.net.NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                return activeNetworkInfo != null && activeNetworkInfo.isConnected();
            }
            
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking network connectivity", e);
            return false;
        }
    }
    
    /**
     * Provides diagnostic information for troubleshooting
     */
    public static String getDiagnosticInfo(Context context, String projectId, String serviceAccountJson) {
        StringBuilder diagnostic = new StringBuilder();
        
        diagnostic.append("=== Authentication Diagnostic Report ===\n\n");
        
        // Network status
        diagnostic.append("Network Status: ");
        diagnostic.append(isNetworkAvailable(context) ? "Connected" : "Disconnected");
        diagnostic.append("\n\n");
        
        // Project ID validation
        diagnostic.append("Project ID Validation:\n");
        CredentialValidator.ValidationResult projectValidation = 
            CredentialValidator.validateProjectId(projectId);
        diagnostic.append("  Valid: ").append(projectValidation.isValid()).append("\n");
        if (!projectValidation.isValid()) {
            diagnostic.append("  Error: ").append(projectValidation.getErrorMessage()).append("\n");
        }
        diagnostic.append("\n");
        
        // Service Account JSON validation
        diagnostic.append("Service Account JSON Validation:\n");
        CredentialValidator.ValidationResult jsonValidation = 
            CredentialValidator.validateServiceAccountJson(serviceAccountJson);
        diagnostic.append("  Valid: ").append(jsonValidation.isValid()).append("\n");
        if (!jsonValidation.isValid()) {
            diagnostic.append("  Error: ").append(jsonValidation.getErrorMessage()).append("\n");
        }
        diagnostic.append("\n");
        
        // Project ID match validation
        if (projectValidation.isValid() && jsonValidation.isValid()) {
            diagnostic.append("Project ID Match Validation:\n");
            CredentialValidator.ValidationResult matchValidation = 
                CredentialValidator.validateProjectIdMatch(projectId, serviceAccountJson);
            diagnostic.append("  Match: ").append(matchValidation.isValid()).append("\n");
            if (!matchValidation.isValid()) {
                diagnostic.append("  Error: ").append(matchValidation.getErrorMessage()).append("\n");
            }
            diagnostic.append("\n");
        }
        
        // System information
        diagnostic.append("System Information:\n");
        diagnostic.append("  Android Version: ").append(android.os.Build.VERSION.RELEASE).append("\n");
        diagnostic.append("  API Level: ").append(android.os.Build.VERSION.SDK_INT).append("\n");
        diagnostic.append("  Device: ").append(android.os.Build.MANUFACTURER)
                  .append(" ").append(android.os.Build.MODEL).append("\n");
        
        return diagnostic.toString();
    }
    
    /**
     * Suggests solutions based on common authentication issues
     */
    public static String getSuggestedSolutions() {
        return "Common Solutions:\n\n" +
               "1. Verify your service account JSON is valid and complete\n" +
               "2. Ensure the project ID matches the one in your service account\n" +
               "3. Check that the Cloud Speech-to-Text API is enabled in your project\n" +
               "4. Verify your service account has the necessary permissions\n" +
               "5. Check your internet connection\n" +
               "6. Try again in a few minutes if the service is temporarily unavailable\n" +
               "7. Contact support if the issue persists";
    }
}
