package com.citrus.audio.auth;

import android.util.Log;

/**
 * Test utility to help debug credential validation issues
 */
public class CredentialValidationTest {
    private static final String TAG = "CredentialValidationTest";
    
    /**
     * Test the credential validation with your specific JSON
     */
    public static void testYourCredentials() {
        String projectId = "aslt-433211";
        String serviceAccountJson = "{\n" +
            "  \"type\": \"service_account\",\n" +
            "  \"project_id\": \"aslt-433211\",\n" +
            "  \"private_key_id\": \"af1c7b2bf7aeb9c7d3d73866d7725cfb90863d6d\",\n" +
            "  \"private_key\": \"-----BEGIN PRIVATE KEY-----\\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDoqJ6ryHa7aFQh\\nFO0eV4qek2Cw84HaIwnDQjhPUWNgD89Xz0PWjdAGwP7VY5cfTXi8ph7P48ooSvc3\\nzr13UFwMH7FhUL6WxezS5jB2XJX0d2i0uFwAk1NR1BBZ/Pyg/uzpnPSqdJKTjjvW\\noxoMCf9yv68043CW1N6FK8y5g1bZ1dtFfP7IZ/RqaUuRMt6iHfl2mPwAFfCkSZm/\\nrkDFHj3S7WRfOJkCvccbaZCM73hyAkVD5dvauYeoCbzFa68EJpajHTF+ZwWFtrdn\\nrknCUKdb+9+/T9RMF5irCwQG2gJYkQc9xiE0lHYjhfMKCerGdDnrm7xxROiVCtH1\\nXKcxv5aDAgMBAAECggEAbuKmtRLW290Oc7HojVSFwaxWIh/8nH2kkoZGFi+hIFCI\\nrr4fA+wU4JMtIYDUrtualrbJ6qUTXX7VMqj7DhmjtTeJr+ru2yc8BwwkvtTDt38N\\nzUwstS2CQXbD0E/OC+FUAseGqJLkpSTeCh80M9a0A/mtxJQ9vfV4lJnaefRJ+YmV\\nt0b1RAppwz/oEhldIOHtmEkEt3hDDG6xSVyaEv /CGXm6sfrzfyF3DbpKSbVN/13Z\\ntGiPLwUhlapMBIMet+MbV3IM5IdEmjZ9cZSAIp2mkPXl4ytzL2qGfG3Oq8IQCnJl\\nFzQuAxPgtB0RdzM44lALnylCI1pOo83mE3lqYpNDQQKBgQD75LEj9cWbLhmPqQrt\\nXrFWHzqKF6+9GmUit29OLs6WYJR6I6TvgmrugJYM0Gu8jTf/VYE5RuTlKMZcrWog\\nsQPCIKhp/GRM3xYsN9p4L0kanVBRfKPUaS4YjQNW72DI/aiuQB5fa3qqyYeNWd4N\\ne4+naQF6y+VvLifoOxjUvgEj4QKBgQDsc6ZOBENgLijoQUiC3s1rgCnLVGOXPIqV\\nHGBCsHUiHxpFpPRvkEPYljm52JB9PybL5lvimW8dbfqbNFSBTz6STF40d9JfVfp2\\nz6gi+8zEXZf8LPESbTqkGQthQAVgTEpqb2E+3qpIQfT5sCpr1w1W5CjlYR0SIdj9\\nG6DrvJWG4wKBgQDQw908XHmF/1MYCZYQ3SZEpmUyBieUpsejWjxZo9l8MFdeUyRj\\njMdrHA9AV4yzHKeIaylKe0NB2eVs8hy/M3ZV8hMipBfMzifmRfQ/H5GLCU6D+rUd\\nWn7lwhk+d+7TJcgjguipVL65xYUe1Zh/vCR3L0QWrd5dovdYNYKC72aqgQKBgQCn\\nLR1oC5uXZQuHKqEZ5m1WlEb4e7HuO9+/jVCB1kD63wK0OuyX9GgDujMWMwf1hbDo\\nePZ+P/1P0XJeSaF1y5Fx3oy8i/sxbJipaCh4k48zRvkuUpgfbxsG6q4hFCK48zeq\\nCRW4XbYHYJrT7roPhPzpcmd3xjHHoCbKzb347lzKAQKBgFTf6aNwOgpzh5o8Oga9\\njfOB6Vaa2Sk3cakG4U79KmrmttYClWdJp2WMHVVnw+giRJEi1psvJgUWPgcF1PwZ\\nsMqHQencru1sm4GIBd/Ji63zQvKahx6PX8qsDAhywLbSEfTps1NNcmQLW2yEmTHM\\nu+Q8/hS7856tfMvAaju6JXlb\\n-----END PRIVATE KEY-----\\n\",\n" +
            "  \"client_email\": \"*******\",\n" +
            "  \"client_id\": \"115863013482295066997\",\n" +
            "  \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n" +
            "  \"token_uri\": \"https://oauth2.googleapis.com/token\",\n" +
            "  \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n" +
            "  \"client_x509_cert_url\": \"https://www.googleapis.com/robot/v1/metadata/x509/speech-text%40aslt-433211.iam.gserviceaccount.com\",\n" +
            "  \"universe_domain\": \"googleapis.com\"\n" +
            "}";
        
        Log.d(TAG, "Testing credential validation...");
        
        // Test project ID validation
        CredentialValidator.ValidationResult projectResult = CredentialValidator.validateProjectId(projectId);
        Log.d(TAG, "Project ID validation: " + projectResult.isValid() + 
              (projectResult.isValid() ? "" : " - " + projectResult.getErrorMessage()));
        
        // Test service account JSON validation
        CredentialValidator.ValidationResult jsonResult = CredentialValidator.validateServiceAccountJson(serviceAccountJson);
        Log.d(TAG, "Service Account JSON validation: " + jsonResult.isValid() + 
              (jsonResult.isValid() ? "" : " - " + jsonResult.getErrorMessage()));
        
        // Test project ID match
        CredentialValidator.ValidationResult matchResult = CredentialValidator.validateProjectIdMatch(projectId, serviceAccountJson);
        Log.d(TAG, "Project ID match validation: " + matchResult.isValid() + 
              (matchResult.isValid() ? "" : " - " + matchResult.getErrorMessage()));
        
        // Test the private key specifically
        testPrivateKeyValidation(serviceAccountJson);
    }
    
    /**
     * Specifically test private key validation
     */
    private static void testPrivateKeyValidation(String serviceAccountJson) {
        try {
            org.json.JSONObject json = new org.json.JSONObject(serviceAccountJson);
            String privateKey = json.getString("private_key");
            
            Log.d(TAG, "Original private key length: " + privateKey.length());
            Log.d(TAG, "Private key starts with: " + privateKey.substring(0, Math.min(50, privateKey.length())));
            Log.d(TAG, "Private key contains \\n: " + privateKey.contains("\\n"));
            Log.d(TAG, "Private key contains actual newlines: " + privateKey.contains("\n"));
            
            // Test with normalization
            String normalizedPrivateKey = privateKey.replace("\\n", "\n");
            Log.d(TAG, "Normalized private key length: " + normalizedPrivateKey.length());
            Log.d(TAG, "Normalized private key starts with: " + normalizedPrivateKey.substring(0, Math.min(50, normalizedPrivateKey.length())));
            
            // Test regex patterns
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("-----BEGIN PRIVATE KEY-----[\\s\\S]*-----END PRIVATE KEY-----");
            boolean originalMatches = pattern.matcher(privateKey).matches();
            boolean normalizedMatches = pattern.matcher(normalizedPrivateKey).matches();
            
            Log.d(TAG, "Original private key matches regex: " + originalMatches);
            Log.d(TAG, "Normalized private key matches regex: " + normalizedMatches);
            
            // Test basic structure
            boolean hasBeginMarker = privateKey.contains("-----BEGIN PRIVATE KEY-----");
            boolean hasEndMarker = privateKey.contains("-----END PRIVATE KEY-----");
            Log.d(TAG, "Has BEGIN marker: " + hasBeginMarker);
            Log.d(TAG, "Has END marker: " + hasEndMarker);
            
        } catch (Exception e) {
            Log.e(TAG, "Error testing private key validation", e);
        }
    }
    
    /**
     * Test with the exact JSON as it would appear when pasted into a text field
     */
    public static void testPastedJson() {
        String pastedJson = "{\"type\":\"service_account\",\"project_id\":\"aslt-433211\",\"private_key_id\":\"af1c7b2bf7aeb9c7d3d73866d7725cfb90863d6d\",\"private_key\":\"-----BEGIN PRIVATE KEY-----\\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDoqJ6ryHa7aFQh\\nFO0eV4qek2Cw84HaIwnDQjhPUWNgD89Xz0PWjdAGwP7VY5cfTXi8ph7P48ooSvc3\\nzr13UFwMH7FhUL6WxezS5jB2XJX0d2i0uFwAk1NR1BBZ/Pyg/uzpnPSqdJKTjjvW\\noxoMCf9yv68043CW1N6FK8y5g1bZ1dtFfP7IZ/RqaUuRMt6iHfl2mPwAFfCkSZm/\\nrkDFHj3S7WRfOJkCvccbaZCM73hyAkVD5dvauYeoCbzFa68EJpajHTF+ZwWFtrdn\\nrknCUKdb+9+/T9RMF5irCwQG2gJYkQc9xiE0lHYjhfMKCerGdDnrm7xxROiVCtH1\\nXKcxv5aDAgMBAAECggEAbuKmtRLW290Oc7HojVSFwaxWIh/8nH2kkoZGFi+hIFCI\\nrr4fA+wU4JMtIYDUrtualrbJ6qUTXX7VMqj7DhmjtTeJr+ru2yc8BwwkvtTDt38N\\nzUwstS2CQXbD0E/OC+FUAseGqJLkpSTeCh80M9a0A/mtxJQ9vfV4lJnaefRJ+YmV\\nt0b1RAppwz/oEhldIOHtmEkEt3hDDG6xSVyaEv/CGXm6sfrzfyF3DbpKSbVN/13Z\\ntGiPLwUhlapMBIMet+MbV3IM5IdEmjZ9cZSAIp2mkPXl4ytzL2qGfG3Oq8IQCnJl\\nFzQuAxPgtB0RdzM44lALnylCI1pOo83mE3lqYpNDQQKBgQD75LEj9cWbLhmPqQrt\\nXrFWHzqKF6+9GmUit29OLs6WYJR6I6TvgmrugJYM0Gu8jTf/VYE5RuTlKMZcrWog\\nsQPCIKhp/GRM3xYsN9p4L0kanVBRfKPUaS4YjQNW72DI/aiuQB5fa3qqyYeNWd4N\\ne4+naQF6y+VvLifoOxjUvgEj4QKBgQDsc6ZOBENgLijoQUiC3s1rgCnLVGOXPIqV\\nHGBCsHUiHxpFpPRvkEPYljm52JB9PybL5lvimW8dbfqbNFSBTz6STF40d9JfVfp2\\nz6gi+8zEXZf8LPESbTqkGQthQAVgTEpqb2E+3qpIQfT5sCpr1w1W5CjlYR0SIdj9\\nG6DrvJWG4wKBgQDQw908XHmF/1MYCZYQ3SZEpmUyBieUpsejWjxZo9l8MFdeUyRj\\njMdrHA9AV4yzHKeIaylKe0NB2eVs8hy/M3ZV8hMipBfMzifmRfQ/H5GLCU6D+rUd\\nWn7lwhk+d+7TJcgjguipVL65xYUe1Zh/vCR3L0QWrd5dovdYNYKC72aqgQKBgQCn\\nLR1oC5uXZQuHKqEZ5m1WlEb4e7HuO9+/jVCB1kD63wK0OuyX9GgDujMWMwf1hbDo\\nePZ+P/1P0XJeSaF1y5Fx3oy8i/sxbJipaCh4k48zRvkuUpgfbxsG6q4hFCK48zeq\\nCRW4XbYHYJrT7roPhPzpcmd3xjHHoCbKzb347lzKAQKBgFTf6aNwOgpzh5o8Oga9\\njfOB6Vaa2Sk3cakG4U79KmrmttYClWdJp2WMHVVnw+giRJEi1psvJgUWPgcF1PwZ\\nsMqHQencru1sm4GIBd/Ji63zQvKahx6PX8qsDAhywLbSEfTps1NNcmQLW2yEmTHM\\nu+Q8/hS7856tfMvAaju6JXlb\\n-----END PRIVATE KEY-----\\n\",\"client_email\":\"*******\",\"client_id\":\"115863013482295066997\",\"auth_uri\":\"https://accounts.google.com/o/oauth2/auth\",\"token_uri\":\"https://oauth2.googleapis.com/token\",\"auth_provider_x509_cert_url\":\"https://www.googleapis.com/oauth2/v1/certs\",\"client_x509_cert_url\":\"https://www.googleapis.com/robot/v1/metadata/x509/speech-text%40aslt-433211.iam.gserviceaccount.com\",\"universe_domain\":\"googleapis.com\"}";
        
        Log.d(TAG, "Testing pasted JSON validation...");
        
        CredentialValidator.ValidationResult result = CredentialValidator.validateServiceAccountJson(pastedJson);
        Log.d(TAG, "Pasted JSON validation result: " + result.isValid() + 
              (result.isValid() ? "" : " - " + result.getErrorMessage()));
    }
}
