package com.citrus.audio.auth;

import android.util.Log;
import org.json.JSONObject;
import org.json.JSONException;
import java.util.regex.Pattern;

/**
 * Utility class for validating Google Cloud service account credentials
 */
public class CredentialValidator {
    private static final String TAG = "CredentialValidator";

    // Regex patterns for validation
    private static final Pattern PROJECT_ID_PATTERN = Pattern.compile("^[a-z][a-z0-9-]{4,28}[a-z0-9]$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    private static final Pattern PRIVATE_KEY_PATTERN = Pattern
            .compile("-----BEGIN PRIVATE KEY-----[\\s\\S]*-----END PRIVATE KEY-----");

    /**
     * Validation result class
     */
    public static class ValidationResult {
        private final boolean isValid;
        private final String errorMessage;
        private final String fieldName;

        public ValidationResult(boolean isValid, String errorMessage, String fieldName) {
            this.isValid = isValid;
            this.errorMessage = errorMessage;
            this.fieldName = fieldName;
        }

        public boolean isValid() {
            return isValid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getFieldName() {
            return fieldName;
        }
    }

    /**
     * Validates project ID format
     */
    public static ValidationResult validateProjectId(String projectId) {
        if (projectId == null || projectId.trim().isEmpty()) {
            return new ValidationResult(false, "Project ID cannot be empty", "project_id");
        }

        String trimmedProjectId = projectId.trim();

        if (!PROJECT_ID_PATTERN.matcher(trimmedProjectId).matches()) {
            return new ValidationResult(false,
                    "Project ID must be 6-30 characters, start with a letter, and contain only lowercase letters, numbers, and hyphens",
                    "project_id");
        }

        return new ValidationResult(true, null, null);
    }

    /**
     * Validates service account JSON comprehensively
     */
    public static ValidationResult validateServiceAccountJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return new ValidationResult(false, "Service account JSON cannot be empty", "service_account_json");
        }

        try {
            JSONObject json = new JSONObject(jsonString.trim());

            // Validate type field
            ValidationResult typeResult = validateType(json);
            if (!typeResult.isValid()) {
                return typeResult;
            }

            // Validate project_id field
            ValidationResult projectIdResult = validateJsonProjectId(json);
            if (!projectIdResult.isValid()) {
                return projectIdResult;
            }

            // Validate private_key_id field
            ValidationResult privateKeyIdResult = validatePrivateKeyId(json);
            if (!privateKeyIdResult.isValid()) {
                return privateKeyIdResult;
            }

            // Validate private_key field
            ValidationResult privateKeyResult = validatePrivateKey(json);
            if (!privateKeyResult.isValid()) {
                return privateKeyResult;
            }

            // Validate client_email field
            ValidationResult clientEmailResult = validateClientEmail(json);
            if (!clientEmailResult.isValid()) {
                return clientEmailResult;
            }

            // Validate client_id field
            ValidationResult clientIdResult = validateClientId(json);
            if (!clientIdResult.isValid()) {
                return clientIdResult;
            }

            // Validate auth_uri field
            ValidationResult authUriResult = validateAuthUri(json);
            if (!authUriResult.isValid()) {
                return authUriResult;
            }

            // Validate token_uri field
            ValidationResult tokenUriResult = validateTokenUri(json);
            if (!tokenUriResult.isValid()) {
                return tokenUriResult;
            }

            return new ValidationResult(true, null, null);

        } catch (JSONException e) {
            Log.e(TAG, "Invalid JSON format", e);
            return new ValidationResult(false, "Invalid JSON format: " + e.getMessage(), "service_account_json");
        }
    }

    private static ValidationResult validateType(JSONObject json) {
        try {
            if (!json.has("type")) {
                return new ValidationResult(false, "Missing 'type' field", "type");
            }

            String type = json.getString("type");
            if (!"service_account".equals(type)) {
                return new ValidationResult(false, "Type must be 'service_account'", "type");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'type' field", "type");
        }
    }

    private static ValidationResult validateJsonProjectId(JSONObject json) {
        try {
            if (!json.has("project_id")) {
                return new ValidationResult(false, "Missing 'project_id' field", "project_id");
            }

            String projectId = json.getString("project_id");
            return validateProjectId(projectId);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'project_id' field", "project_id");
        }
    }

    private static ValidationResult validatePrivateKeyId(JSONObject json) {
        try {
            if (!json.has("private_key_id")) {
                return new ValidationResult(false, "Missing 'private_key_id' field", "private_key_id");
            }

            String privateKeyId = json.getString("private_key_id");
            if (privateKeyId.trim().isEmpty()) {
                return new ValidationResult(false, "Private key ID cannot be empty", "private_key_id");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'private_key_id' field", "private_key_id");
        }
    }

    private static ValidationResult validatePrivateKey(JSONObject json) {
        try {
            if (!json.has("private_key")) {
                return new ValidationResult(false, "Missing 'private_key' field", "private_key");
            }

            String privateKey = json.getString("private_key");

            // Handle both actual newlines and escaped newlines (\n)
            String normalizedPrivateKey = privateKey.replace("\\n", "\n");

            // Check if it matches the expected private key format
            boolean isValidFormat = PRIVATE_KEY_PATTERN.matcher(normalizedPrivateKey).matches() ||
                    PRIVATE_KEY_PATTERN.matcher(privateKey).matches();

            // Additional validation: check for basic structure
            if (!isValidFormat) {
                // Check if it at least contains the BEGIN and END markers
                boolean hasBeginMarker = privateKey.contains("-----BEGIN PRIVATE KEY-----") ||
                        normalizedPrivateKey.contains("-----BEGIN PRIVATE KEY-----");
                boolean hasEndMarker = privateKey.contains("-----END PRIVATE KEY-----") ||
                        normalizedPrivateKey.contains("-----END PRIVATE KEY-----");

                if (!hasBeginMarker || !hasEndMarker) {
                    return new ValidationResult(false, "Invalid private key format: missing BEGIN or END markers",
                            "private_key");
                }

                // If markers are present but regex doesn't match, it might be a formatting
                // issue
                if (privateKey.trim().isEmpty()) {
                    return new ValidationResult(false, "Private key cannot be empty", "private_key");
                }

                // Accept it if it has the basic structure (this is more lenient for user input)
                Log.d(TAG, "Private key has basic structure but doesn't match strict regex - accepting");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'private_key' field", "private_key");
        }
    }

    private static ValidationResult validateClientEmail(JSONObject json) {
        try {
            if (!json.has("client_email")) {
                return new ValidationResult(false, "Missing 'client_email' field", "client_email");
            }

            String clientEmail = json.getString("client_email");
            if (!EMAIL_PATTERN.matcher(clientEmail).matches()) {
                return new ValidationResult(false, "Invalid client email format", "client_email");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'client_email' field", "client_email");
        }
    }

    private static ValidationResult validateClientId(JSONObject json) {
        try {
            if (!json.has("client_id")) {
                return new ValidationResult(false, "Missing 'client_id' field", "client_id");
            }

            String clientId = json.getString("client_id");
            if (clientId.trim().isEmpty()) {
                return new ValidationResult(false, "Client ID cannot be empty", "client_id");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'client_id' field", "client_id");
        }
    }

    private static ValidationResult validateAuthUri(JSONObject json) {
        try {
            if (!json.has("auth_uri")) {
                return new ValidationResult(false, "Missing 'auth_uri' field", "auth_uri");
            }

            String authUri = json.getString("auth_uri");
            if (!authUri.startsWith("https://")) {
                return new ValidationResult(false, "Auth URI must be HTTPS", "auth_uri");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'auth_uri' field", "auth_uri");
        }
    }

    private static ValidationResult validateTokenUri(JSONObject json) {
        try {
            if (!json.has("token_uri")) {
                return new ValidationResult(false, "Missing 'token_uri' field", "token_uri");
            }

            String tokenUri = json.getString("token_uri");
            if (!tokenUri.startsWith("https://")) {
                return new ValidationResult(false, "Token URI must be HTTPS", "token_uri");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Invalid 'token_uri' field", "token_uri");
        }
    }

    /**
     * Validates that project IDs match between user input and JSON
     */
    public static ValidationResult validateProjectIdMatch(String userProjectId, String jsonString) {
        ValidationResult projectIdValidation = validateProjectId(userProjectId);
        if (!projectIdValidation.isValid()) {
            return projectIdValidation;
        }

        try {
            JSONObject json = new JSONObject(jsonString);
            String jsonProjectId = json.getString("project_id");

            if (!userProjectId.trim().equals(jsonProjectId)) {
                return new ValidationResult(false,
                        "Project ID mismatch: entered '" + userProjectId + "' but JSON contains '" + jsonProjectId
                                + "'",
                        "project_id_match");
            }

            return new ValidationResult(true, null, null);
        } catch (JSONException e) {
            return new ValidationResult(false, "Cannot extract project_id from JSON", "project_id_match");
        }
    }
}
