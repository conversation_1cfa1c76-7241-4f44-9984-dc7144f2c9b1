package com.citrus.audio;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.support.v4.app.ActivityCompat;
import android.support.v4.content.ContextCompat;
import android.support.v7.app.AlertDialog;
import android.support.v7.app.AppCompatActivity;
import android.text.Html;
import android.text.InputType;
import android.text.method.LinkMovementMethod;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.citrus.audio.asr.CloudSpeechSessionParams;
import com.citrus.audio.asr.CloudSpeechStreamObserverParams;
import com.citrus.audio.asr.RepeatingRecognitionSession;
import com.citrus.audio.asr.SafeTranscriptionResultFormatter;
import com.citrus.audio.asr.SpeechRecognitionModelOptions;
import com.citrus.audio.asr.TranscriptionResultFormatterOptions;
import com.citrus.audio.asr.TranscriptionResultUpdatePublisher;
import com.citrus.audio.asr.cloud.CloudSpeechSessionFactory;
import com.citrus.audio.auth.GoogleCloudAuthManager;
import com.citrus.audio.auth.CredentialValidator;
import com.citrus.audio.auth.CredentialValidationTest;
import com.citrus.audio.R;

public class MainActivity extends AppCompatActivity {

    private static final int PERMISSIONS_REQUEST_RECORD_AUDIO = 1;

    private static final int MIC_CHANNELS = AudioFormat.CHANNEL_IN_MONO;
    private static final int MIC_CHANNEL_ENCODING = AudioFormat.ENCODING_PCM_16BIT;
    private static final int MIC_SOURCE = MediaRecorder.AudioSource.VOICE_RECOGNITION;
    private static final int SAMPLE_RATE = 16000;
    private static final int CHUNK_SIZE_SAMPLES = 1280;
    private static final int BYTES_PER_SAMPLE = 2;

    private static AudioRecord audioRecord;
    private static final byte[] buffer = new byte[BYTES_PER_SAMPLE * CHUNK_SIZE_SAMPLES];

    private static RepeatingRecognitionSession recognizer;
    private static NetworkConnectionChecker networkChecker;
    private static TextView transcript;
    private static TranscriptionResultUpdatePublisher transcriptUpdater;

    public static String VERSION = "1.0.2";
    public static String transcribedText = "";
    public static String SERVICE_ACCOUNT_JSON = ""; // Will be set by user input
    public static String PROJECT_ID = ""; // Will be set by user input
    public static int currentLanguageCodePosition;
    public static String currentLanguageCode = "ar-SA";

    // Authentication manager instance
    private static GoogleCloudAuthManager authManager = new GoogleCloudAuthManager();

    private static Runnable readMicData = () -> {
        if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED)
            return;
        recognizer.init(CHUNK_SIZE_SAMPLES);
        while (audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
            audioRecord.read(buffer, 0, CHUNK_SIZE_SAMPLES * BYTES_PER_SAMPLE);
            recognizer.processAudioBytes(buffer);
        }
        recognizer.stop();
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        transcript = findViewById(R.id.transcript);
        setupTranscriptUpdater(this);

        Button startBtn = findViewById(R.id.startBtn);
        Button stopBtn = findViewById(R.id.stopBtn);

        startBtn.setOnClickListener(v -> startListening());
        stopBtn.setOnClickListener(v -> stopListening());
    }

    private void startListening() {
        initAudioTranscription(this);
    }

    public static void stopListening() {
        transcribedText = "";
        if (transcript != null) {
            transcript.setText("");
        }
        onDispose();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (ContextCompat.checkSelfPermission(this,
                Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                    this,
                    new String[] { Manifest.permission.RECORD_AUDIO },
                    PERMISSIONS_REQUEST_RECORD_AUDIO);
        } else {
            showAPIKeyDialog(this);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (audioRecord != null) {
            audioRecord.stop();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (recognizer != null) {
            recognizer.unregisterCallback(transcriptUpdater);
        }
        if (networkChecker != null) {
            networkChecker.unregisterNetworkCallback();
        }
        if (authManager != null) {
            authManager.clearCredentials();
        }
    }

    public static void onDispose() {
        transcribedText = "";
        if (audioRecord != null) {
            audioRecord.stop();
        }
        if (recognizer != null) {
            recognizer.unregisterCallback(transcriptUpdater);
        }
        if (networkChecker != null) {
            networkChecker.unregisterNetworkCallback();
        }
    }

    @Override
    public void onRequestPermissionsResult(
            int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == PERMISSIONS_REQUEST_RECORD_AUDIO) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showAPIKeyDialog(this);
            } else {
                Toast.makeText(
                        this,
                        "This app does not work without the Microphone permission.",
                        Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    public static void setCurrentLanguageCode(String languageCode) {
        currentLanguageCode = languageCode;
        Log.d("setCurrentLanguageCode", currentLanguageCode);
    }

    public static void initAudioTranscription(Activity mActivity) {
        onDispose();
        setupTranscriptUpdater(mActivity);
        showAPIKeyDialog(mActivity);
    }

    public static void setupTranscriptUpdater(Activity activity) {
        transcriptUpdater = (formattedTranscript, updateType) -> {
            activity.runOnUiThread(() -> {
                transcribedText = formattedTranscript.toString();
                if (transcript != null) {
                    transcript.setText(transcribedText);
                }
            });
        };
    }

    public static void constructRepeatingRecognitionSession(final Context context) {
        try {
            SpeechRecognitionModelOptions options = SpeechRecognitionModelOptions.newBuilder()
                    .setLocale(currentLanguageCode)
                    .setModel(currentLanguageCode.equals("en-US") ? SpeechRecognitionModelOptions.SpecificModel.VIDEO
                            : SpeechRecognitionModelOptions.SpecificModel.DICTATION_DEFAULT)
                    .build();

            CloudSpeechSessionParams cloudParams = CloudSpeechSessionParams.newBuilder()
                    .setObserverParams(CloudSpeechStreamObserverParams.newBuilder()
                            .setRejectUnstableHypotheses(false))
                    .setFilterProfanity(true)
                    .build();

            networkChecker = new NetworkConnectionChecker(context);
            networkChecker.registerNetworkCallback();

            TranscriptionResultFormatterOptions formatterOptions = TranscriptionResultFormatterOptions.newBuilder()
                    .setTranscriptColoringStyle(TranscriptionResultFormatterOptions.TranscriptColoringStyle.NO_COLORING)
                    .build();

            recognizer = RepeatingRecognitionSession.newBuilder()
                    .setSpeechSessionFactory(
                            new CloudSpeechSessionFactory(cloudParams, SERVICE_ACCOUNT_JSON, PROJECT_ID))
                    .setSampleRateHz(SAMPLE_RATE)
                    .setTranscriptionResultFormatter(new SafeTranscriptionResultFormatter(formatterOptions))
                    .setSpeechRecognitionModelOptions(options)
                    .setNetworkConnectionChecker(networkChecker)
                    .build();

            if (transcriptUpdater != null && recognizer != null) {
                recognizer.registerCallback(transcriptUpdater,
                        TranscriptionResultUpdatePublisher.ResultSource.WHOLE_RESULT);
            }

        } catch (Exception e) {
            Log.e("SpeechRecognizerUtil", "Error constructing recognition session: ", e);
        }
    }

    public static void startRecording() {
        if (audioRecord == null) {
            audioRecord = new AudioRecord(
                    MIC_SOURCE,
                    SAMPLE_RATE,
                    MIC_CHANNELS,
                    MIC_CHANNEL_ENCODING,
                    CHUNK_SIZE_SAMPLES * BYTES_PER_SAMPLE);
        }

        if (audioRecord != null) {
            audioRecord.startRecording();
            new Thread(readMicData).start();
        }
    }

    public static void showAPIKeyDialog(Context context) {
        LayoutInflater inflater = LayoutInflater.from(context);
        View contentLayout = inflater.inflate(R.layout.api_key_message, null);

        EditText projectIdInput = contentLayout.findViewById(R.id.project_id_input);
        EditText serviceAccountInput = contentLayout.findViewById(R.id.service_account_json_input);
        TextView linkView = contentLayout.findViewById(R.id.api_key_link_view);
        TextView selectLanguageView = contentLayout.findViewById(R.id.language_locale_view);
        Spinner languageSpinner = contentLayout.findViewById(R.id.language_locale_spinner);

        // Pre-fill with current values
        projectIdInput.setText(PROJECT_ID);
        serviceAccountInput.setText(SERVICE_ACCOUNT_JSON);

        linkView.setText(Html.fromHtml(context.getString(R.string.api_key_doc_link)));
        linkView.setMovementMethod(LinkMovementMethod.getInstance());

        selectLanguageView.setText(Html.fromHtml(context.getString(R.string.select_language_message)));
        selectLanguageView.setMovementMethod(LinkMovementMethod.getInstance());

        ArrayAdapter<String> languagesList = new ArrayAdapter<>(
                context,
                android.R.layout.simple_spinner_item,
                context.getResources().getStringArray(R.array.languages));
        languagesList.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        languageSpinner.setAdapter(languagesList);
        languageSpinner.setSelection(currentLanguageCodePosition);

        languageSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                currentLanguageCodePosition = position;
                currentLanguageCode = context.getResources().getStringArray(R.array.language_locales)[position];
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });

        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("Configure Google Cloud")
                .setView(contentLayout)
                .setPositiveButton("Start", (dialog, which) -> {
                    PROJECT_ID = projectIdInput.getText().toString().trim();
                    SERVICE_ACCOUNT_JSON = serviceAccountInput.getText().toString().trim();

                    if (PROJECT_ID.isEmpty() || SERVICE_ACCOUNT_JSON.isEmpty()) {
                        Toast.makeText(context, "Enter Project ID and Service Account JSON.", Toast.LENGTH_SHORT)
                                .show();
                        return;
                    }

                    // Validate credentials before proceeding
                    validateAndAuthenticate(context, PROJECT_ID, SERVICE_ACCOUNT_JSON);
                })
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .setCancelable(false)
                .show();
    }

    /**
     * Validates credentials and authenticates with Google Cloud
     */
    private static void validateAndAuthenticate(Context context, String projectId, String serviceAccountJson) {
        // First, validate the input format
        CredentialValidator.ValidationResult projectIdValidation = CredentialValidator.validateProjectId(projectId);
        if (!projectIdValidation.isValid()) {
            Toast.makeText(context, "Invalid Project ID: " + projectIdValidation.getErrorMessage(),
                    Toast.LENGTH_LONG).show();
            return;
        }

        CredentialValidator.ValidationResult jsonValidation = CredentialValidator
                .validateServiceAccountJson(serviceAccountJson);
        if (!jsonValidation.isValid()) {
            Toast.makeText(context, "Invalid Service Account JSON: " + jsonValidation.getErrorMessage(),
                    Toast.LENGTH_LONG).show();
            return;
        }

        CredentialValidator.ValidationResult matchValidation = CredentialValidator.validateProjectIdMatch(projectId,
                serviceAccountJson);
        if (!matchValidation.isValid()) {
            Toast.makeText(context, matchValidation.getErrorMessage(), Toast.LENGTH_LONG).show();
            return;
        }

        // Show progress to user
        Toast.makeText(context, "Authenticating with Google Cloud...", Toast.LENGTH_SHORT).show();

        // Authenticate with Google Cloud
        authManager.setCredentials(projectId, serviceAccountJson, new GoogleCloudAuthManager.AuthenticationCallback() {
            @Override
            public void onSuccess(String message) {
                // Run on UI thread
                if (context instanceof Activity) {
                    ((Activity) context).runOnUiThread(() -> {
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                        // Proceed with speech recognition setup
                        constructRepeatingRecognitionSession(context);
                        startRecording();
                    });
                }
            }

            @Override
            public void onError(GoogleCloudAuthManager.AuthenticationError error) {
                // Run on UI thread
                if (context instanceof Activity) {
                    ((Activity) context).runOnUiThread(() -> {
                        Toast.makeText(context, "Authentication failed: " + error.getMessage(),
                                Toast.LENGTH_LONG).show();
                        Log.e("MainActivity", "Authentication error: " + error.getMessage());
                    });
                }
            }
        });
    }

    /**
     * Gets the service account JSON (for internal use only)
     */
    public static String getServiceAccountJson() {
        return SERVICE_ACCOUNT_JSON;
    }

    /**
     * Test method to debug credential validation - call this from onCreate for
     * testing
     */
    public static void testCredentialValidation() {
        Log.d("MainActivity", "Running credential validation test...");
        CredentialValidationTest.testYourCredentials();
        CredentialValidationTest.testPastedJson();
    }

    /**
     * Get the latest transcribed text
     */
    public static String getLatestTranscription() {
        return transcribedText != null ? transcribedText : "";
    }

    /**
     * Stop recording audio
     */
    public static void stopRecording() {
        if (audioRecord != null) {
            audioRecord.stop();
            audioRecord.release();
            audioRecord = null;
        }
    }

}
