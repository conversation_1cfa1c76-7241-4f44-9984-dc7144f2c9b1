# Builds the ogg and opus encoder library which used third party module.
cmake_minimum_required(VERSION 3.4.1)

project(live_transcription)

# set related path of third party libraries.
set(third_party_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../../../third_party)


#
# set(CMAKE_PREFIX_PATH "/home/<USER>/Documents")
# set(Opus_DIR "/home/<USER>/Documents/share/cmake/Opus")
# find_package(Opus REQUIRED)
#
## Set up Android NDK (adjust paths as needed)

#set(ANDROID_NDK_ROOT "/Users/<USER>/Library/Android/sdk/ndk/21.4.7075529")
#include_directories(${ANDROID_NDK_ROOT}/sources/cxx-stl/gnu-libstdc++/include)
#link_directories(${ANDROID_NDK_ROOT}/toolchains/llvm/prebuilt/linux-x86_64/lib/arm64-v8a)
#
## Add source files
#add_library(transcription_lib SHARED
#        src/main.cpp
#        src/transcription_engine.cpp
#        src/opus_encoder.cpp
#        src/opus_decoder.cpp
#)
#
## Link libraries
#target_link_libraries(transcription_lib
#        Opus::Opus
#        # ... other libraries ...
#)
#
## Generate Android ABI-specific shared libraries
#add_custom_command(
#        OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/lib/arm64-v8a/libtranscription_lib.so
#        COMMAND ${CMAKE_AR} rcu ${CMAKE_CURRENT_BINARY_DIR}/libtranscription_lib.a
#        DEPENDS ${CMAKE_CURRENT_BINARY_DIR}/libtranscription_lib.a
#)



# libogg library refer from https://github.com/xiph/ogg.
set(libogg_INCLUDE ${third_party_DIR}/libogg/include)
set(libogg_LIB ${third_party_DIR}/libogg/lib/${ANDROID_ABI})
add_library(lib_ogg STATIC IMPORTED)
set_target_properties(lib_ogg PROPERTIES IMPORTED_LOCATION ${libogg_LIB}/libogg.a)

# libopus library refer from https://github.com/xiph/opus.
set(libopus_INCLUDE ${third_party_DIR}/libopus/include)
set(libopus_LIB ${third_party_DIR}/libopus/lib/${ANDROID_ABI})
add_library(lib_opus STATIC IMPORTED)
set_target_properties(lib_opus PROPERTIES IMPORTED_LOCATION ${libopus_LIB}/libopus.a)

# opus-tools https://github.com/xiph/opus-tools.
set(opus_tools_INCLUDE ${third_party_DIR}/opus_tools/include)
set(opus_tools_LIB ${third_party_DIR}/opus_tools/lib/${ANDROID_ABI})
add_library(lib_opus_header STATIC IMPORTED)
set_target_properties(lib_opus_header PROPERTIES IMPORTED_LOCATION
        ${opus_tools_LIB}/libopus_header.a)

# Build ogg_opus_encoder_tool shared lib.
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=gnu++11")
add_library(ogg_opus_encoder_tool SHARED ogg_opus_encoder.cc)
target_include_directories(ogg_opus_encoder_tool PRIVATE ${libogg_INCLUDE})
target_link_libraries(ogg_opus_encoder_tool lib_opus lib_ogg lib_opus_header)
