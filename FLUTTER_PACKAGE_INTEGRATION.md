# Flutter Package Integration Guide

This guide explains how to properly integrate the ASL Android Input library as an .aar file into your Flutter package.

## Package Structure

Your Flutter package should have the following structure:

```
your_flutter_package/
├── lib/
│   ├── asl_flutter_input.dart
│   └── asl_flutter_input_platform_interface.dart
├── android/
│   ├── libs/
│   │   └── asl_v1.0.x.aar
│   ├── src/main/java/com/citrus/audio/
│   │   └── AslFlutterInputPlugin.java
│   └── build.gradle
├── pubspec.yaml
└── README.md
```

## 1. pubspec.yaml Configuration

```yaml
name: asl_flutter_input
description: Flutter plugin for ASL Android Input library providing real-time speech-to-text transcription.
version: 1.0.0

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=2.5.0"

dependencies:
  flutter:
    sdk: flutter
  plugin_platform_interface: ^2.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.citrus.audio
        pluginClass: AslFlutterInputPlugin
```

## 2. Android build.gradle Configuration

Create `android/build.gradle`:

```gradle
group 'com.citrus.audio'
version '1.0.0'

buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
    }

    dependencies {
        implementation 'androidx.annotation:annotation:1.3.0'
        
        // Include the .aar file
        implementation files('libs/asl_v1.0.x.aar')
        
        // Required dependencies that are NOT included in the .aar
        implementation('com.google.cloud:google-cloud-speech:4.69.0') {
            exclude group: 'commons-codec', module: 'commons-codec'
            exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
            exclude group: 'com.google.guava', module: 'guava'
        }
        
        implementation 'commons-codec:commons-codec:1.15'
        implementation 'com.google.guava:guava:31.1-android'
        implementation 'com.google.errorprone:error_prone_annotations:2.18.0'
        
        implementation 'io.grpc:grpc-okhttp:1.71.0'
        implementation 'io.grpc:grpc-core:1.71.0'
        implementation 'io.grpc:grpc-stub:1.71.0'
        
        implementation 'com.google.auth:google-auth-library-oauth2-http:1.39.0'
        implementation 'com.google.auth:google-auth-library-credentials:1.39.0'
        implementation 'com.google.auth:google-auth-library-appengine:1.39.0'
        
        implementation 'com.google.flogger:flogger:0.4'
        implementation 'com.google.flogger:flogger-system-backend:0.4'
        implementation 'com.google.protobuf:protobuf-java:3.22.3'
        implementation 'com.google.protobuf:protobuf-java-util:3.22.3'
        implementation 'joda-time:joda-time:2.9.2'
        implementation 'androidx.multidex:multidex:2.0.1'
    }
}
```

## 3. Dart Platform Interface

Create `lib/asl_flutter_input_platform_interface.dart`:

```dart
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'asl_flutter_input_method_channel.dart';

abstract class AslFlutterInputPlatform extends PlatformInterface {
  AslFlutterInputPlatform() : super(token: _token);

  static final Object _token = Object();
  static AslFlutterInputPlatform _instance = MethodChannelAslFlutterInput();

  static AslFlutterInputPlatform get instance => _instance;

  static set instance(AslFlutterInputPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String> configure(String projectId, String serviceAccountJson);
  Future<String> initAudioTranscription();
  Future<String> stopListening();
  Future<String> getTranscribedText();
  Future<String> setLanguageCode(String languageCode);
  Future<String> testAuthentication(String projectId, String serviceAccountJson);
  Future<String> getDiagnosticReport();
  Future<bool> isConfigured();
  Future<bool> isListening();
  Future<String> getVersion();
  Future<String> reset();
}
```

## 4. Method Channel Implementation

Create `lib/asl_flutter_input_method_channel.dart`:

```dart
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'asl_flutter_input_platform_interface.dart';

class MethodChannelAslFlutterInput extends AslFlutterInputPlatform {
  @visibleForTesting
  final methodChannel = const MethodChannel('com.citrus.audio/asl_flutter_input');

  @override
  Future<String> configure(String projectId, String serviceAccountJson) async {
    final result = await methodChannel.invokeMethod<String>('configure', {
      'projectId': projectId,
      'serviceAccountJson': serviceAccountJson,
    });
    return result ?? 'Configuration completed';
  }

  @override
  Future<String> initAudioTranscription() async {
    final result = await methodChannel.invokeMethod<String>('initAudioTranscription');
    return result ?? 'Audio transcription initialized';
  }

  @override
  Future<String> stopListening() async {
    final result = await methodChannel.invokeMethod<String>('stopListening');
    return result ?? 'Listening stopped';
  }

  @override
  Future<String> getTranscribedText() async {
    final result = await methodChannel.invokeMethod<String>('getTranscribedText');
    return result ?? '';
  }

  @override
  Future<String> setLanguageCode(String languageCode) async {
    final result = await methodChannel.invokeMethod<String>('setLanguageCode', {
      'languageCode': languageCode,
    });
    return result ?? 'Language code set';
  }

  @override
  Future<String> testAuthentication(String projectId, String serviceAccountJson) async {
    final result = await methodChannel.invokeMethod<String>('testAuthentication', {
      'projectId': projectId,
      'serviceAccountJson': serviceAccountJson,
    });
    return result ?? 'Authentication test completed';
  }

  @override
  Future<String> getDiagnosticReport() async {
    final result = await methodChannel.invokeMethod<String>('getDiagnosticReport');
    return result ?? 'No diagnostic report available';
  }

  @override
  Future<bool> isConfigured() async {
    final result = await methodChannel.invokeMethod<bool>('isConfigured');
    return result ?? false;
  }

  @override
  Future<bool> isListening() async {
    final result = await methodChannel.invokeMethod<bool>('isListening');
    return result ?? false;
  }

  @override
  Future<String> getVersion() async {
    final result = await methodChannel.invokeMethod<String>('getVersion');
    return result ?? 'Unknown';
  }

  @override
  Future<String> reset() async {
    final result = await methodChannel.invokeMethod<String>('reset');
    return result ?? 'Reset completed';
  }
}
```

## 5. Main Plugin File

Create `lib/asl_flutter_input.dart`:

```dart
import 'asl_flutter_input_platform_interface.dart';

class AslFlutterInput {
  /// Configure the library with project ID and service account JSON
  static Future<String> configure(String projectId, String serviceAccountJson) {
    return AslFlutterInputPlatform.instance.configure(projectId, serviceAccountJson);
  }

  /// Initialize audio transcription
  static Future<String> initAudioTranscription() {
    return AslFlutterInputPlatform.instance.initAudioTranscription();
  }

  /// Stop listening for audio
  static Future<String> stopListening() {
    return AslFlutterInputPlatform.instance.stopListening();
  }

  /// Get the current transcribed text
  static Future<String> getTranscribedText() {
    return AslFlutterInputPlatform.instance.getTranscribedText();
  }

  /// Set the language code for speech recognition
  static Future<String> setLanguageCode(String languageCode) {
    return AslFlutterInputPlatform.instance.setLanguageCode(languageCode);
  }

  /// Test authentication with provided credentials
  static Future<String> testAuthentication(String projectId, String serviceAccountJson) {
    return AslFlutterInputPlatform.instance.testAuthentication(projectId, serviceAccountJson);
  }

  /// Get diagnostic report for troubleshooting
  static Future<String> getDiagnosticReport() {
    return AslFlutterInputPlatform.instance.getDiagnosticReport();
  }

  /// Check if the library is configured
  static Future<bool> isConfigured() {
    return AslFlutterInputPlatform.instance.isConfigured();
  }

  /// Check if the library is currently listening
  static Future<bool> isListening() {
    return AslFlutterInputPlatform.instance.isListening();
  }

  /// Get the library version
  static Future<String> getVersion() {
    return AslFlutterInputPlatform.instance.getVersion();
  }

  /// Reset the library configuration
  static Future<String> reset() {
    return AslFlutterInputPlatform.instance.reset();
  }
}
```

## Integration Steps

1. **Generate the .aar file** from your Android library project
2. **Place the .aar file** in `android/libs/` of your Flutter package
3. **Copy the AslFlutterInputPlugin.java** to `android/src/main/java/com/citrus/audio/`
4. **Create the Dart files** as shown above
5. **Update build.gradle** to include all required dependencies
6. **Test the integration** with a sample Flutter app

## Important Notes

- The .aar file contains the core library functionality but NOT Flutter dependencies
- The Flutter plugin class (AslFlutterInputPlugin.java) handles method channel registration
- All Google Cloud Speech dependencies must be included in the Flutter package's build.gradle
- The consuming Flutter app doesn't need to add any additional dependencies
