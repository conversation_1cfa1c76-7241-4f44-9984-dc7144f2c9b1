# Gradle files
.gradle/
!gradle/wrapper/gradle-wrapper.jar
**/out/

# Android Studio files
.idea/
*.iml
*.ipr
*.iws

# Local configuration file (sdk path, etc)
local.properties

# Log and binary files
*.log
*.keystore
*.jks


# Android specific files
/app/build/

/app/.cxx/

# Kotlin specific files
*.kotlin_module

# Node (if you're using React Native or other Node-based tooling)
node_modules/

# MacOS specific files
.DS_Store

# Windows specific files
Thumbs.db

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws
.idea/modules.xml
.idea/workspace.xml
.idea/navEditor.xml
.idea/assetWizardSettings.xml
.idea/gradle.xml
.idea/libraries
.idea/caches
.idea/tasks.xml
.idea/dictionaries
.idea/vcs.xml
.idea/jsLibraryMappings.xml
.idea/misc.xml

# Plugin specific files
# Comment the next line if you are using Android Studio 4.1+ or Android Gradle Plugin 7+
**/debug.log
